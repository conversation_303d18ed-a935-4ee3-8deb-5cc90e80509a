import { get } from 'jquery'
import { postRequest, getRequest, postFormData, postParamsQS, save } from '../http.js'
// 服务前缀
const PrefixService = __PATH.BASE_URL_HSC
export default {
  // 基础管理
  getDictByPage: postFormData('materialsDict/materialTypeTree', PrefixService), // 字典管理
  saveDictInfo: postFormData('materialsDict/materialTypeAdd', PrefixService), // 字典新增
  updateDictInfo: postFormData('materialsDict/materialTypeUpdate', PrefixService), // 字典编辑
  delDictInfo: postFormData('materialsDict/materialTypeDel', PrefixService), // 字典删除
  getDictNewByPage: getRequest('web/prDictionaryDetailsD/getSuperiorData', __PATH.VUE_SPACE_API), // 字典管理新
  // 耗材管理
  getConsumableByPage: postFormData('materialsConsumable/listData', PrefixService), // 耗材列表查询
  getConsumableStatus: postFormData('materialsConsumable/enableOrDisable', PrefixService), // 耗材启用/停用
  delConsumableInfo: postFormData('materialsConsumable/delete', PrefixService), // 耗材列表删除
  saveOrUpdateConsumableInfo: postFormData('materialsConsumable/save', PrefixService), // 耗材新增/修改
  exportConsumableInfo: postFormData('materialsConsumable/export', PrefixService), // 耗材导出
  getConsumableById: postFormData('materialsConsumable/view', PrefixService), // 耗材详情
  // 库房管理
  getWarehouseByPage: postFormData('warehouse/listData', PrefixService), // 库房列表
  delWarehouseInfo: postFormData('warehouse/delete', PrefixService), // 库房删除
  getWarehouseStatus: postFormData('warehouse/enableOrDisable', PrefixService), // 库房启用/停用
  saveOrUpdateWarehouseInfo: postFormData('warehouse/save', PrefixService), // 库房新增/修改
  getWarehouseById: postFormData('warehouse/view', PrefixService), // 库房详情
  // 入库管理
  getWarehouseEntryList: postFormData('inwarehouseRecord/listData', PrefixService), // 入库管理列表
  getWarehouseEntrydelete: postFormData('inwarehouseRecord/delete', PrefixService), // 入库管理列表删除
  importConsumableExcelCheck: postFormData('inwarehouseRecord/importConsumableExcelCheck', PrefixService), // 耗材批量导入
  queryWorkOrderByPage: getRequest('sys/taskmanagementnew/olgTaskManagement/findPageNew', __PATH.VUE_IOMS_API), // 工单列表
  inwarehouseRecordSave: postFormData('inwarehouseRecord/save', PrefixService), // 入库管理新增
  getOCTeamMemberInfo: postParamsQS('iHCRSStatisticsController/getOCTeamMemberInfo', __PATH.VUE_IOMS_API),
  getOfficeAll: postParamsQS('sys/taskmanagementnew/olgTaskManagement/getOfficeAll', __PATH.VUE_IOMS_API), // 申报科室
  getWarehouseDetailByPage: postFormData('inwarehouseRecord/inwarehouseDetailList', PrefixService), // 入库明细列表
  getInWarehouseRecordById: postFormData('inwarehouseRecord/view', PrefixService), // 入库单详情
  approveInWarehouseRecord: postFormData('inwarehouseRecord/approve', PrefixService), // 入库单审核
  // 出库管理
  getOutwarehouseRecordList: postFormData('outwarehouseRecord/listData', PrefixService), // 出库管理列表
  getInventoryDetailByPage: postFormData('warehouse/getInventoryDetailByPage', PrefixService), // 查询库存请单
  getInOutListByPage: postFormData('warehouse/findInOutListPages', PrefixService), // 查询出入库记录
  outwarehouseRecordSave: postFormData('outwarehouseRecord/save', PrefixService), // 出库管理新增
  getOutWarehouseRecordById: postFormData('outwarehouseRecord/view', PrefixService), // 出库单详情
  approveOutWarehouseRecord: postFormData('outwarehouseRecord/approve', PrefixService), // 出库单审核
  getOutwarehouseDetailByPage: postFormData('outwarehouseRecord/outwarehouseDetailList', PrefixService), // 出库明细列表
  // 库存管理
  getInventoryWarningConfigListByPage: postFormData('inventoryWarningConfig/getInventoryWarningConfigListByPage', PrefixService), // 查询库存预警列表
  saveInventoryWarningConfigInfo: postRequest('inventoryWarningConfig/saveInventoryWarningConfig', PrefixService), // 保存库存预警配置
  getInventoryListByPage: postFormData('inventoryManage/getInventoryListByPage', PrefixService), // 查询库存列表
  getInOutRecordListByPage: postFormData('inventoryManage/getInOutRecordListByPage', PrefixService), // 查询出入库记录详情
  // 盘点管理
  getTakeStockByPage: postRequest('takeStock/getTakeStockList', PrefixService), // 查询盘点列表
  deTakeStockInfo: postRequest('takeStock/deleteTakeStockByIds', PrefixService), // 批量删除盘点
  saveTakeStockInfo: postRequest('takeStock/saveTakeStock', PrefixService), // 保存盘点
  getTakeStockById: postRequest('takeStock/getTakeStockInfo', PrefixService), // 盘点详情
  getTakeStockConsumablesByPage: postFormData('inventoryManage/getInventoryListForApp', PrefixService) // 盘点专用选择耗材接口
}
