import {
  deleteFn,
  downFile,
  downFileAQ,
  getQueryQS,
  getRequest,
  getSecurity,
  getSecurityType,
  iemc_downFile,
  postFile,
  postFormData,
  postParamsDR,
  postParamsQS,
  postParamsQSSecurity,
  postParamsQueryString,
  postParamsSecurity,
  postParamsTY,
  postQs,
  postQuarters,
  postQuartersSecurity,
  postQuartersTY,
  postQueryQS,
  postRequest,
  postRequestCS,
  postSaveSecurity,
  postURL,
  post_iemc,
  post_iemc_notInfo,
  post_isp_iaast,
  post_formData,
  post_json,
  getHttp,
  put,
  websocketService,
  getSecurityData,
  uploadCommon
} from './http.js'
import RentalHousingApi from '@/api/module/rental-housing-api'
import SporadicProject from './module/sporadic-project-api'
import construciton from './module/construciton'
import fileManagement from './module/fileManagement.js'
import warehouseApi from './module/warehouseApi.js'
import oneStopApi from './module/oneStopApi.js'
export default {
  AlarmPush: websocketService('alarmServer/', __PATH.WS_ALARM_URL), // 告警推送
  IemcWebsocket: websocketService('imserver/', __PATH.WS_IEMC_URL), // 火灾音视频、给排水、空调监测推送
  elevatorWebsocket: websocketService('iemcEleServer/', __PATH.WS_ELEVATOR_URL), // 电梯推送
  homeOperWorkOrderWebSocketServer: websocketService('homeOperWorkOrderWebSocketServer', __PATH.WS_IOMS_URL), // 待办事项实时刷新
  noticeMessageWebSocketServer: websocketService('noticeMessageWebSocketServer', __PATH.WS_IOMS_URL), // 通知消息实时刷新
  homeRefreshBacklogWebSocketServer: websocketService('homeRefreshBacklogWebSocketServer', __PATH.WS_IOMS_URL), // 刷新待办事项实时刷新
  loginSystem: postRequest('sysLogin/login', __PATH.VUE_SYS_API), // 登录
  logoutSystem: postRequest('sysLogin/logout', __PATH.VUE_SYS_API), // 退出登录
  resetPassword: postRequest('loginApp/resetByMobile', __PATH.VUE_SYS_API), // 退出登录
  getPhoneCode: (phone) => getRequest(`smsApp/UPDATE_PASSWORD/${phone}`, __PATH.VUE_SYS_API), // 获取验证码
  // 检查验证码
  smsCodeCheck: postParamsQS('smsApp/smsCodeCheck', __PATH.VUE_SYS_API),
  // ---------------------------------------------------------------------------------------------------角色管理
  getSysRoleInfo: postRequest('sysRole/getSysRoleInfoList', __PATH.VUE_SYS_API), // 获取角色列表
  getSysRoleInfoByCode: getQueryQS('sysRole/getRoleInfo', __PATH.VUE_SYS_API), // 获取角色详情
  insertRoleInfo: postRequest('sysRole/insertRoleInfo', __PATH.VUE_SYS_API), // 新增角色
  updateRoleInfo: postRequest('sysRole/updateRoleInfo', __PATH.VUE_SYS_API), // 编辑角色
  updateRolePower: postRequest('sysRole/updateRolePower', __PATH.VUE_SYS_API), // 编辑角色 修改 菜单 数据权限
  removeOrDisuseSysRole: postRequest('sysRole/removeOrDisuseSysRole', __PATH.VUE_SYS_API), // 删除角色
  getMenuTreeData: postRequest('SysMenu/index', __PATH.VUE_SYS_API), // 获取菜单树
  getStructureTree: getRequest('space/structure/structureTree', __PATH.VUE_SPACE_API), //
  getSpaceInfoList: postQuarters('space/spaceInfo/list', __PATH.VUE_SPACE_API), //
  GetRoomInfoList: postQuarters('space/model/searchRoomInfo', __PATH.VUE_SPACE_API), // 通过楼层code和功能类型以及空间名称搜索房屋信息列表
  lookUpDataById: getQueryQS('space/spaceInfo/lookUpById', __PATH.VUE_SPACE_API), //
  GetAllAlarmRecord: postRequest('alarm/record/selectAlarmRecordAll', __PATH.VUE_WARN_API), // 获取全部报警记录
  queryAllAlarmStatisticsCount: postRequest('alarm/record/queryAllAlarmStatisticsCount', __PATH.VUE_WARN_API), // 获取全部报警统计
  GetMyAlarmRecord: postRequest('alarm/record/queryMyAlarm', __PATH.VUE_WARN_API), // 获取我报警记录
  GetMyAlarmStatisticsCount: postRequest('alarm/record/queryMyAlarmStatisticsCount', __PATH.VUE_WARN_API), // 获取我的报警统计
  GetAlarmRecord: postRequest('alarm/record/selectAlarmRecordPage', __PATH.VUE_WARN_API), // 分类获取报警记录
  selectAlarmRecord: postRequest('alarm/record/selectAlarmRecord', __PATH.VUE_WARN_API), // 分类获取报警记录
  GetAlarmDetails: postRequest('alarm/record/selectAlarmRecordById', __PATH.VUE_WARN_API), // 获取报警详情
  CollectAlarmRecords: postRequest('alarm/record/setAlarmRecordToClassics', __PATH.VUE_WARN_API), // 将报警记录存为经典案例
  AlarmAffirm: postRequest('alarm/record/updateAlarmAffirmById', __PATH.VUE_WARN_API), // 确警
  CloseAlarmRecord: postRequest('alarm/record/closeAlarmRecordById', __PATH.VUE_WARN_API), // 关闭报警记录
  GetAlarmRecordClassics: postRequest('alarm/record/selectAlarmRecordClassics', __PATH.VUE_WARN_API), // 获取经典案例列表
  // GetIncidentAndSourceGroup: getQueryQS('alarm/record/getIncidentAndSourceGroup', __PATH.VUE_WARN_API), // 获取事件类型以及报警来源分组 (弃用)
  getSourceByEmpty: getQueryQS('alarm/record/getSource', __PATH.VUE_WARN_API), // 获取事件类型以及报警来源分组
  getIncidentGroupByProjectCode: getQueryQS('alarm/record/getIncidentGroup', __PATH.VUE_WARN_API), // 根据消息来源查询事件类型
  getAlarmTypeByProjectCode: getQueryQS('alarm/record/getAlarmTypeByProjectCode', __PATH.VUE_WARN_API), // 根据消息来源查询事件类型
  OneKeyDispatch: postRequest('alarm/record/oneKeyDispatch', __PATH.VUE_WARN_API), // 一键派单
  handleAlarmAffirmById: postRequest('alarm/record/handleAlarmAffirmById', __PATH.VUE_WARN_API), // 处理报警
  limWorkInfo: postRequest('alarm/record/limWorkInfo', __PATH.VUE_WARN_API),
  taskList: postRequest('alarm/record/getInspectionTaskVoPage', __PATH.VUE_WARN_API),
  summarySave: postRequest('alarm/alarmSummary/save', __PATH.VUE_WARN_API), // 总结分析
  videoList: postFormData('cameraManage/getCameraListByImsId', __PATH.VUE_IEMC_API), // 报警监控画面
  picRecording: postRequest('alarmFile/queryLimAlarmFile', __PATH.VUE_WARN_API), // 报警录像
  getWorkOrderToAdd: postParamsQS('iHCRSOperOrderController/toAdd', __PATH.VUE_IOMS_API),
  getWorkOrderOper: postQueryQS('iHCRSOperOrderController/oper', __PATH.VUE_IOMS_API),
  getItemTreeData: postParamsQS('iHCRSStatisticsController/bootstrapTreeData', __PATH.VUE_IOMS_API),
  getAllOffice: postParamsQS('iHCRSStatisticsController/getAllOffice', __PATH.VUE_IOMS_API),
  getGridTreeData: postParamsQS('sys/taskmanagementnew/olgTaskManagement/getNewAllSpaceInfo', __PATH.VUE_IOMS_API),
  getDataByTypeTeam: postParamsQS('iHCRSStatisticsController/getOCTeamInfo', __PATH.VUE_IOMS_API),
  getServicePersonName: postParamsQS('iHCRSStatisticsController/getOCTeamMemberInfo', __PATH.VUE_IOMS_API),
  getEmergencyBook: postParamsQS('iHCRSOperOrderController/getEmergencyBook', __PATH.VUE_IOMS_API),
  getWorkOrderDetail: postParamsQS('iHCRSStatisticsController/getWorkOrderDetail', __PATH.VUE_IOMS_API),
  getSelfOrderDetails: postParamsQS('cleaningWorkOrder/getDetailsFixByOwn', __PATH.VUE_IOMS_API),
  // getWorkOrderDetail: postParamsQS('appDisOlgTask.do?getTaskDetail', __PATH.VUE_IOMS_API),
  getIomsDictList: postParamsQS('iHCRSOperOrderController/getDictList', __PATH.VUE_IOMS_API),
  workOrderOperOrder: postParamsQS('sys/home/<USER>/operOrder', __PATH.VUE_IOMS_API),
  placeAndCancelOrder: postParamsQS('sys/taskmanagementnew/olgTaskManagement/placeAndCancelOrder', __PATH.VUE_IOMS_API),
  placeAndCancelSave: postParamsQS('iHCRSOperOrderController/save', __PATH.VUE_IOMS_API),
  getIsRequired: postParamsQS('appOlgTaskManagement/getIsRequired', __PATH.VUE_IOMS_API),
  toTeamsChangeTask: postParamsQS('sys/taskmanagementnew/olgTaskManagement/toTeamsChangeTask', __PATH.VUE_IOMS_API),
  addFeedback: postParamsQS('sys/taskmanagementnew/olgTaskManagement/addFeedback', __PATH.VUE_IOMS_API),
  updateTask: postParamsQS('sys/taskmanagementnew/olgTaskManagement/updateTask', __PATH.VUE_IOMS_API),
  appRollbackTask: postParamsQS('sys/taskmanagementnew/olgTaskManagement/pcRollbackTask', __PATH.VUE_IOMS_API),
  getfactMaterialTreeData: postParamsQS('iHCRSOperOrderController/treeData', __PATH.VUE_IOMS_API),
  dispatchingTheWorkOrder: postParamsQS('deviceRepair/dispatchingTheWorkOrder', __PATH.VUE_IOMS_API), // 工单指派（认领）
  getNewStaffInfoById: postParamsQS('iHCRSStatisticsController/getNewStaffInfoById', __PATH.VUE_IOMS_API), // 工单指派（认领）
  getConsumablesList: postParamsQS('iHCRSOperOrderController/getConsumablesList', __PATH.VUE_IOMS_API),
  suspendTheWorkOrder: postParamsQS('deviceRepair/suspendTheWorkOrder', __PATH.VUE_IOMS_API), // 工单挂单
  getConsumables: postParamsQS('deviceRepair/getConsumables'), // 获取完工耗材
  getMalfunctionReasonMethod: postParamsQS('iHCRSOperOrderController/getTreeData', __PATH.VUE_IOMS_API),
  saveMalfunctionReasonMethod: postParamsQS('appDisOlgTask.do?saveMalfunctionReasonMethod', __PATH.VUE_IOMS_API),
  saveAlarmWorkOrder: postParamsQS('appOlgTaskManagement.do?saveAlarmWorkOrder', __PATH.VUE_IOMS_API),
  pdEvaluationOk: postParamsQS('sys/taskmanagementnew/olgTaskManagement/updEvaluationOk', __PATH.VUE_IOMS_API),
  taskByServiceItemTop10: postParamsQS('olgTaskStatisticsController/taskByServiceItemTop10', __PATH.VUE_IOMS_API),
  taskByOfficeTop5: postParamsQS('olgTaskStatisticsController/taskByOfficeTop5', __PATH.VUE_IOMS_API),
  taskByBuildingTop5: postParamsQS('olgTaskStatisticsController/taskByBuildingTop5', __PATH.VUE_IOMS_API),
  getMonthComparison: postParamsQS('olgTaskStatisticsController/getMonthComparison', __PATH.VUE_IOMS_API),
  uploadFiles: postFile('minio/upload', __PATH.VUE_IOMS_API, 'formdata'), // 上传文件
  preplanDrillTaskUploadFiles: postFile('preplanDrillTask/upload', __PATH.VUE_PLAN_API, 'formdata'), // 上传文件
  getLimAlarmLinkageByProject: postParamsDR('limAlarmLinkage/getLimAlarmLinkageByProject', __PATH.VUE_SYS_API), // 查询联动配置信息
  saveAlarmLinkage: postParamsDR('limAlarmLinkage/saveAlarmLinkage', __PATH.VUE_SYS_API), // 保存联动配置信息
  getSelected: getRequest('unitManager/unit-manager/getSelected', __PATH.VUE_SPACE_API), // 联动配置查询医院
  getSelectedDept: getRequest('departmentManager/department-manager/getSelectedDept', __PATH.VUE_SPACE_API), // 根据所属单位回去部门列表
  staffList: postQuarters('hospitalStaff/hospital-staff/list'), // 对应人员
  queryUserListByPage: postQuarters('userInfo/queryUserListByPage', __PATH.VUE_SYS_API), // 对应人员
  // selectByList: postQuarters('postManager/post-manager/selectByList'), // 获取岗位列表
  selectAlarmOperationOrRemarkById: postQuarters('alarm/operation/selectAlarmOperationOrRemarkById', __PATH.VUE_WARN_API), // 查询备注
  insertRemarkById: postQuarters('alarm/operation/insertRemarkById', __PATH.VUE_WARN_API),
  shield: postQuarters('alarm/record/shield', __PATH.VUE_WARN_API), // 屏蔽
  SysMenu: postQuarters('SysMenu/index', __PATH.VUE_SYS_API),
  SysMenuList: postQuarters('SysMenu/list', __PATH.VUE_SYS_API),
  indexSelect: postQuarters('SysMenu/indexSelect', __PATH.VUE_SYS_API),
  menuTreeData: getRequest('SysMenu/menuTreeData', __PATH.VUE_SYS_API),
  menuAdd: postQuarters('SysMenu/add', __PATH.VUE_SYS_API),
  remove: getRequest('SysMenu/remove', __PATH.VUE_SYS_API),
  removeOrDisuseMenu: postQuarters('SysMenu/removeOrDisuseMenu', __PATH.VUE_SYS_API),
  getMenuInfo: getRequest('SysMenu/getMenuInfo', __PATH.VUE_SYS_API),
  edit: postQuarters('SysMenu/edit', __PATH.VUE_SYS_API),
  createDeviceRepair: postParamsQS('deviceRepair/createWorkOrder', __PATH.VUE_IOMS_API),
  getOrderDeptList: postParamsQS('team/pageList', __PATH.VUE_IOMS_API),
  getItemTop5: postParamsQS('sys/taskmanagementnew/olgTaskManagement/getItemTop5', __PATH.VUE_IOMS_API),
  getNewSpaceInfoByDeptId: postParamsQS('sys/taskmanagementnew/olgTaskManagement/getNewSpaceInfoByDeptId', __PATH.VUE_IOMS_API),
  getDeptByLocalCode: postParamsQS('sys/taskmanagementnew/olgTaskManagement/getDeptByLocalCode', __PATH.VUE_IOMS_API),
  getNewSysConfigParam: postParamsQS('appDisOlgTask/getNewSysConfigParam', __PATH.VUE_IOMS_API),
  getInformationByPhone: postParamsQS('sys/home/<USER>/getInformationByPhone', __PATH.VUE_IOMS_API),
  getListColumnConfig: postRequest('listColumnConfig/getListColumnConfig', __PATH.VUE_IOMS_API),
  callCenterDataNew: postParamsQS('sys/home/<USER>/callCenterDataNew', __PATH.VUE_IOMS_API),
  callCenterDataNewCount: postParamsQS('sys/home/<USER>/callCenterDataNewCount', __PATH.VUE_IOMS_API),
  saveListColumnConfig: postRequest('listColumnConfig/save', __PATH.VUE_IOMS_API),
  getBacklogList: postParamsQS('sys/home/<USER>/getBacklogList', __PATH.VUE_IOMS_API),
  getWorkOrderTimeoutList: postParamsQS('iHCRSStatisticsController/getWorkOrderTimeoutList', __PATH.VUE_IOMS_API),
  getOCTeamMemberInfo: getRequest('sys/taskmanagementnew/olgTaskManagement/getOCTeamMemberInfo', __PATH.VUE_IOMS_API),
  // ---------------------------------------------报警统计---------------------------------------------
  getPoliceInfoByApp: getRequest('alarm/record/getPoliceInfoByApp', __PATH.VUE_WARN_API),
  getPolicePieByApp: getRequest('alarm/record/getPolicePieByApp', __PATH.VUE_WARN_API),
  getAlarmTopCount: getRequest('alarm/record/getAlarmTopCount', __PATH.VUE_WARN_API),
  getAlarmSourceCount: getRequest('alarm/record/getAlarmSourceCount', __PATH.VUE_WARN_API),
  selectBoilerAlarmRecord: postRequest('alarm/record/selectBoilerAlarmRecord', __PATH.VUE_WARN_API),
  getAlarmTrendPc: postRequest('alarm/record/getAlarmTrendPc', __PATH.VUE_WARN_API),
  getAlarmTrendPcByParam: postRequest('alarm/record/getAlarmTrendPcByParam', __PATH.VUE_WARN_API),
  selectEntityType: postRequest('alarm/record/selectEntityType', __PATH.VUE_WARN_API),
  selectIncidentParam: postRequest('alarm/record/selectIncidentParam', __PATH.VUE_WARN_API),
  selectAlarmList: postRequest('alarm/record/selectAlarmList', __PATH.VUE_WARN_API),
  selectDiffProjectCodeAlarm: postRequest('alarm/record/selectDiffProjectCodeAlarm', __PATH.VUE_WARN_API),
  selectFaultPie: postRequest('alarm/record/selectFaultPie', __PATH.VUE_WARN_API),
  // ---------------------------------------------工作台---------------------------------------------
  getSysTime: postRequest('weather/getSysTime', __PATH.VUE_SYS_API), // 获取系统时间
  getWeatherByCityCode: postRequest('weather/getWeatherByCityCode', __PATH.VUE_SYS_API), // 获取天气
  getNewsLabelList: postRequest('newsSubscribe/getNewsLabelList', __PATH.VUE_SYS_API), // 获取订阅消息列表
  saveOrUpdateNewsSubscribe: postRequest('newsSubscribe/saveOrUpdate', __PATH.VUE_SYS_API), // 修改订阅消息
  getMessageByUserId: postRequest('message/getMessageByUserId', __PATH.VUE_NEWS_API), // 获取消息列表
  insertToDoList: postRequest('message/insertToDoList', __PATH.VUE_NEWS_API), // 新增待办
  updateToDoList: postRequest('message/updateToDoList', __PATH.VUE_NEWS_API), // 修改待办
  selectOutStoreRecordStatistics: postParamsQS('ihcrsInterfaceController/selectOutStoreRecordStatistics', __PATH.VUE_APP_IMWS_API), // 获取医废出库记录统计
  getTaskEquipmentMap: postParamsQueryString('planTaskNew/getTaskEquipmentMap', __PATH.VUE_ICIS_API), // 后勤设备巡检任务统计
  getTaskQuantity: postParamsQueryString('planTaskNew/taskQuantity', __PATH.VUE_ICIS_API), // 后勤设备巡检任务完成率
  updateReadStatus: postRequest('message/updateReadStatus', __PATH.VUE_NEWS_API), // 已读未读状态改变
  onekeyreadpc: getRequest('message/oneKeyReadPc', __PATH.VUE_NEWS_API), // 一键已读
  finishToDoListLevel: postRequest('message/finishToDoListLevel', __PATH.VUE_NEWS_API), // 待办事项完成状态改变
  deleteMsgByUserId: getRequest('message/deleteMsgByUserId', __PATH.VUE_NEWS_API), // 删除消息
  imserverPush: websocketService('imserver/', __PATH.WS_NEWS_API), // 消息推送
  getImserverMsgInfo: getRequest('message/getMsgInfo', __PATH.VUE_NEWS_API), // 获取消息详情
  getMessageListApp: getRequest('message/getMessageListApp', __PATH.VUE_SYS_API), // 获取未读消息
  getTaskmentList: getRequest('appOlgTaskManagement?getWorkTypeList', __PATH.VUE_IOMS_API), // 获取工单类型
  getPersonnelDictionary: postParamsQS('appOlgTaskManagement.do?getPersonnelDictionary', __PATH.VUE_IOMS_API), // 获取人员字典
  getWorktopManageList: postRequest('worktopManage/getWorktopManageList', __PATH.VUE_SYS_API), // 获取工作台管理列表
  saveWorktopManage: postRequest('worktopManage/saveWorktopManage', __PATH.VUE_SYS_API), // 保存工作台管理  // ----------------------------------------------------------------------------------------------------------监测
  getScadaList: post_iemc('entityMenu/getScalaEntityMenuList'), // 实时监测--scada列表
  getScalaListByIemCode: postRequest('entityMenu/getScalaListByIemCode', __PATH.VUE_IEMC_API), // 实时监测--scada列表(new 分组接口)
  updateSurveyGroup: postFormData('surveyAndParameter/updateSurveyGroup', __PATH.VUE_IEMC_API), // 分组改变接口
  getSurveyImgByCode: postRequest('surveyAndParameter/getSurveyImgByCode', __PATH.VUE_IEMC_API), // 实时监测--scada剖面图
  setScadaToRedis: postRequest('entityMenu/setScadaToRedis', __PATH.VUE_IEMC_API), // 实时监测--scada剖面图
  airCountOverview: postRequest('airCondition/countOverview', __PATH.VUE_IEMC_API), // 总览 空调数据
  getEnvironmentOverview: postRequest('environment/getEnvironmentOverview', __PATH.VUE_IEMC_API), // 总览 总览数据
  getWarnCount: postRequest('alarm/record/airPoliceCount', __PATH.VUE_WARN_API), // 总览 报警数量
  getSurveyParameterList: postRequest('surveyAndParameter/getSurveyParameterList', __PATH.VUE_IEMC_API), // 监测项管理列表
  importSurveyExcel: postFormData('surveyAndParameter/importSurveyExcel', __PATH.VUE_IEMC_API), // 监测实体导入
  importParamScada: postFormData('surveyAndParameter/importParamScada', __PATH.VUE_IEMC_API), // 监测实体scada导入
  exportTemplateExcel: downFile('surveyAndParameter/exportExcel', __PATH.VUE_IEMC_API), // 下载监测项导入模板
  getSurveyParameterOne: postRequest('surveyAndParameter/getSurveyParameterOne', __PATH.VUE_IEMC_API), // 监测项详情
  insertSurveyAndParameter: postRequest('surveyAndParameter/insertSurveyAndParameter', __PATH.VUE_IEMC_API), // 监测项新增
  updateSurveyAndParameter: postRequest('surveyAndParameter/updateSurveyAndParameter', __PATH.VUE_IEMC_API), // 监测项编辑
  delSurveyAndParameter: postRequest('surveyAndParameter/delSurveyAndParameter', __PATH.VUE_IEMC_API), // 监测项删除
  getDictionaryList: postRequest('dictionary/getDictionaryList', __PATH.DICTIONAR_URL), // 获取监测参数字典
  GetSensorTypeList: getRequest('dataServer/getSensorTypeList', __PATH.VUE_IEMC_API), // 第三方数据类型
  getupStreamList: getQueryQS('surveyAndParameter/getupStreamList', __PATH.VUE_IEMC_API), // 获取上游监测实体
  getLevelThreeLinkage: postRequest('scalaDrawing/getLevelThreeLinkage', __PATH.VUE_IEMC_API), // 获取图纸三级联动
  getFolderSelect: postRequest('scalaDrawing/getFolderSelect', __PATH.VUE_IEMC_API), // 查询图纸所属类型--下拉框使用
  getImageListById: postFormData('scalaImage/getImageListById', __PATH.VUE_IEMC_API), // 根据图纸类型id查询数据下拉列表
  getImageSelectById: postFormData('scalaImage/getImageSelectById', __PATH.VUE_IEMC_API), // 根据图纸id查询参数下拉列表
  getDataServerList: postRequest('dataServer/getDataServerList', __PATH.VUE_IEMC_API), // 主机列表查询
  getDataServerListForOtherSys: postRequest('dataServer/getDataServerListForOtherSys', __PATH.VUE_IEMC_API), // 主机列表查询
  testServerNet: postRequest('dataServer/testServerNet', __PATH.VUE_IEMC_API), // 判断测试连接的
  findListByParam: postURL('sensor/findListByParam'), // 根据参数获取传感器
  cameraManageByPage: getQueryQS('cameraManage/cameraManageByPage', __PATH.VUE_IEMC_API), // 摄像机分页查询
  saveCamera: postRequest('cameraManage/saveCamera', __PATH.VUE_IEMC_API), // 摄像机新增
  deleteCameraById: postFormData('cameraManage/deleteById', __PATH.VUE_IEMC_API), // 摄像机删除
  getAllSpaceTree: getQueryQS('space/structure/spaceTree', __PATH.VUE_SPACE_API), // 获取空间树
  getAssetsDetail: getQueryQS('assets/getDetail', __PATH.VUE_SPACE_API), // 基础信息获取设备详情
  // 设备字典信息......................................................
  sysDictType: postParamsQS('sysDictType/listData', __PATH.VUE_ICIS_API), // 设备字典信息
  sysDictData: postParamsQS('sysDictData/listData', __PATH.VUE_ICIS_API),
  getTreeDataList: postParamsQS('sysDictData/getTreeDataList', __PATH.VUE_ICIS_API), // 巡检模板左侧树列表
  saveDictionary: postParamsQS('sysDictData/save', __PATH.VUE_ICIS_API), // 新增字典
  dictionaryDetails: postParamsQS('sysDictData/get', __PATH.VUE_ICIS_API), // 字典详情
  updateDictionary: postParamsQS('sysDictData/update', __PATH.VUE_ICIS_API), // 编辑字典
  deleteDictionary: postParamsQS('sysDictData/delete', __PATH.VUE_ICIS_API), // 删除字典
  equipmentListData: postQuarters('devicetype/equipmentListData', __PATH.VUE_ICIS_API), // 获取类别
  handleClassify: postParamsQS('devicetype/save', __PATH.VUE_ICIS_API), // 字典分类录入
  deleteEquipment: postParamsQS('devicetype/deleteEquipment', __PATH.VUE_ICIS_API), // 字典删除
  getEquipmentList: postQuarters('equipmentParameter/equipmentList', __PATH.VUE_ICIS_API), // 根据设备小类查询 设备参数
  // 模板管理............................................................
  getTemTree: postParamsQS('maintainProjectController/getTemplateClassification', __PATH.VUE_ICIS_API), // 模板分类
  getEquTree: postParamsQS('maintainProjectController/getEquipmentCategory', __PATH.VUE_ICIS_API), // 设备分类
  getTemList: postParamsQS('maintainProjectController/findPage', __PATH.VUE_ICIS_API), // 模板列表
  getDictValueList: postParamsQS('dictUtils/getDictList', __PATH.VUE_ICIS_API), // 字典选项：巡检选项
  addTaskBookProject: postParamsQS('maintainProjectController/addMaintainProject', __PATH.VUE_ICIS_API), // 新增任务书管理
  getTaskBookDetails: postParamsQS('maintainProjectController/findMaintainProjectById', __PATH.VUE_ICIS_API), // 任务书管理查询详情
  icisDeleteTaskBookList: postParamsQS('maintainProjectController/deleteProjectAndDetails', __PATH.VUE_ICIS_API), // 删除任务书
  // 定位点管理
  findLocationPointList: postParamsQS('locationPoint/listData', __PATH.VUE_ICIS_API), // 定位点列表查询
  deleteLocationListList: postParamsQS('locationPoint/delete', __PATH.VUE_ICIS_API), // 删除定位点
  addLocationPoint: postParamsQS('locationPoint/saveOrUpdate', __PATH.VUE_ICIS_API), // 新增/编辑定位点管理
  getLocationPointDetails: postParamsQS('locationPoint/get', __PATH.VUE_ICIS_API), // 定位点管理查询详情
  // (设备、巡检)参数配置
  getConfigurationListData: postQuarters('hospital/configuration/hospitalConfiguration/listData', __PATH.VUE_ICIS_API), //  查看参数配置
  setConfigurationSave: postRequestCS('hospital/configuration/hospitalConfiguration/save', __PATH.VUE_ICIS_API), //  保存参数配置
  getConfigList: getSecurityData('expire_config', __PATH.VUE_ICIS_API), //  设备到期提醒列表
  postConfigSave: post_json('expire_config', __PATH.VUE_ICIS_API), //  设备到期提醒保存
  getDelConfig: getSecurityData('/expire_config/delete_By_ids', __PATH.VUE_ICIS_API), // 设备到期提醒删除
  getDetailsSave: post_json('expire_config/edit', __PATH.VUE_ICIS_API), // 查询详情保存
  getDetails: getSecurityData('expire_config/getById', __PATH.VUE_ICIS_API), // 详情数据
  getDetids: getSecurityData('expire_config/get_exclude_ids', __PATH.VUE_ICIS_API), // 详情数据
  // 设备管理
  getProfessionalCategory: postParamsQS('asset/assetDetails/getProfessionalCategory', __PATH.VUE_ICIS_API), // 获取专业类别
  getAssetList: postParamsQS('asset/assetDetails/getAssetDetails', __PATH.VUE_ICIS_API), // 资产列表数据
  getCategoryList: postRequest('devicetype/get_small_classify', __PATH.VUE_ICIS_API), // 资产上级设备专业类型
  getAssetDetails: postParamsQS('asset/assetDetails/getAssetDetailsById', __PATH.VUE_ICIS_API), // 资产明细数据
  getAssetsIdById: postParamsQS('asset/assetDetails/getAssetsIdById', __PATH.VUE_ICIS_API), // 根据assetsId查id
  getDelAsset: postParamsQS('asset/assetDetails/deleteAssetDetails', __PATH.VUE_ICIS_API), // 删除资产
  selectByListAsset: postQuarters('dictionary/sys-value-dict/selectByList', __PATH.VUE_SPACE_API), // 查询资产字典
  recordList: postQuarters('assets/modifyHistory/list', __PATH.VUE_SPACE_API), // 查询资产操作记录
  getAssetsCountBySys: getQueryQS('assets/getAssetsCountBySys', __PATH.VUE_SPACE_API), // 查询资产数量
  getAssetsList: postQuarters('assets/info/list', __PATH.VUE_SPACE_API), // 查询资产列表
  syncToLocalByAsset: postParamsQS('asset/assetDetails/syncToLocalByAsset', __PATH.VUE_ICIS_API), // 同步到本地
  spaceTree: getQueryQS('space/structure/spaceTree', __PATH.VUE_SPACE_API), // 所在区域
  saveOrUpdate: postParamsQS('asset/assetDetails/saveOrUpdate', __PATH.VUE_ICIS_API), // 添加资产明细
  getDictMenuTree: getQueryQS('dictionary/sys-value-dict/getDictMenuTree', __PATH.VUE_SPACE_API), // 资产分类
  selectByPage: postQuarters('dictionary/sys-value-dict/selectByPage', __PATH.VUE_SPACE_API), // 资产列表
  getOperationDeviceList: postParamsQS('asset/assetDetails/getAssetList', __PATH.VUE_ICIS_API), // 资产列表（运营）
  // 工作日历
  getTaskStatistics: postParamsQS('home/getTaskStatistics', __PATH.VUE_ICIS_API), // 日期维度统计任务数量
  getTaskDynamics: postParamsQS('home/getTaskDynamics', __PATH.VUE_ICIS_API), // 今日任务状态
  selectCalendarList: postParamsQS('home/selectCalendarList', __PATH.VUE_ICIS_API), // 设施设备工作日历列表
  insCalendarList: postParamsQS('home/getComprehensivePatrolInspection', __PATH.VUE_ICIS_API), // 综合工作日历列表
  GetEmployeeDetails: getRequest('hospitalStaff/hospital-staff/getUpdateDetails', __PATH.VUE_SPACE_API), // 查询医院职工详情
  GetJobList: postRequest('jobManager/job-manager/selectByPage', __PATH.VUE_SPACE_API), // 获取职务列表
  AddUserInfo: postRequest('userInfo/insertUserInfo', __PATH.VUE_SYS_API), // 新增用户
  batchInsertUserInfo: postRequest('userInfo/batchInsertUserInfo', __PATH.VUE_SYS_API), // 新增用户
  UpdateUserInfo: postRequest('userInfo/updateUserInfo', __PATH.VUE_SYS_API), // 编辑用户
  GetRoleList: postRequest('sysRole/getSysRoleInfoList', __PATH.VUE_SYS_API), // 获取角色列表
  GetUserInfoList: postRequest('userInfo/getUserInfoList', __PATH.VUE_SYS_API), // 获取用户列表
  UserIsExist: getRequest('userInfo/userIsExist', __PATH.VUE_SYS_API), // 用户是否已存在
  OperationUser: postRequest('userInfo/deleteOrUpdateUser', __PATH.VUE_SYS_API), // 删除/禁用用户
  GetUserDetailInfo: getRequest('userInfo/echoUserInfo', __PATH.VUE_SYS_API), // 获取用户详情
  ResetUserPassWord: getRequest('userInfo/resetPassWord', __PATH.VUE_SYS_API), // 重置用户密码
  UpdateUserAndRole: postRequest('userInfo/updateUserAndRole', __PATH.VUE_SYS_API), // 批量修改用户角色
  ExportUser: downFile('userInfo/export', __PATH.VUE_SYS_API), // 用户导出
  // ----------------------------设备巡检--------------------------------
  getTemplateType: postParamsQS('plan/getPlanTypeStatistics', __PATH.VUE_ICIS_API), // 获取模板分类
  getInspectionTemplate: postParamsQueryString('maintainProjectController/findPage', __PATH.VUE_ICIS_API), // 获取巡检模板
  getLocationPoint: postParamsQueryString('locationPoint/listData', __PATH.VUE_ICIS_API), // 获取定位点列表
  getFunctionType: postRequest('dictionary/sys-value-dict/selectByList', __PATH.VUE_SPACE_API), // 获取空间功能类型分类
  getDeviceType: postParamsQS('devicetype/selectDeviceClassifyList', __PATH.VUE_ICIS_API), // 获取设备分类列表
  getDeviceList: postParamsQS('devicetype/selectDeviceList', __PATH.VUE_ICIS_API), // 获取设备列表
  selectDeviceGroupingList: postParamsQS('devicetype/selectDeviceGroupingList', __PATH.VUE_ICIS_API), // 获取设备列表(新)
  addZdyInspectionPoint: postParamsQS('taskPoint/saveOrUpdate', __PATH.VUE_ICIS_API), // 创建自定义巡检点
  deleteZdyInspectionPoint: postParamsQS('taskPoint/delete', __PATH.VUE_ICIS_API), // 删除自定义巡检点
  getZdyInspectionPointList: postParamsQS('taskPoint/listData', __PATH.VUE_ICIS_API), // 获取自定义巡检点列表
  getTaskPreview: postParamsQueryString('plan/taskPreview', __PATH.VUE_ICIS_API), // 任务预览
  addPlanTask: postParamsQueryString('maintainPlan/addOrUpdateMaintainPlan', __PATH.VUE_ICIS_API), // 新增计划
  getAddPlanTimeAuth: postParamsQueryString('planTaskNew/toDayTask', __PATH.VUE_ICIS_API), // 编辑计划时间校验
  getPlansList: postParamsQueryString('maintainPlan/findPage', __PATH.VUE_ICIS_API), // 计划列表
  getPlanDetail: postParamsQueryString('maintainPlan/getMaintainPlanDetail'), // 获取计划详情
  updateMaintainPlanState: postParamsQueryString('maintainPlan/updateMaintainPlanUseState', __PATH.VUE_ICIS_API), // 修改计划启用/禁用
  getTaskMaintaninList: postParamsQueryString('planTaskNew/listData', __PATH.VUE_ICIS_API), // 任务列表
  findAbnormalTaskPointReleaseList: postParamsQueryString('/taskPointRelease/findAbnormalTaskPointReleaseList', __PATH.VUE_ICIS_API), // 异常点位列表
  getPointTypeStatistics: postParamsQS('taskPointRelease/getPointTypeStatistics', __PATH.VUE_ICIS_API), // 异常点位左侧树列表
  getInspectionPointList: postParamsQueryString('taskPointRelease/listData', __PATH.VUE_ICIS_API), // 巡检点列表
  getInspectionPointDetail: postParamsQueryString('taskPointRelease/detail', __PATH.VUE_ICIS_API), // 巡检点详情
  getTaskPointByTaskId: postParamsQueryString('planTaskNewApiController/taskPointByTaskId', __PATH.VUE_ICIS_API), // 巡检点详情+列表
  deletePlan: postParamsQueryString('plan/deletePlan', __PATH.VUE_ICIS_API), // 计划删除
  deleteTask: postParamsQueryString('planTaskNew/deleteTaskList', __PATH.VUE_ICIS_API), // 任务删除
  taskStatusUpdata: postParamsQueryString('planTaskNew/updTaskState', __PATH.VUE_ICIS_API), // 任务状态修改
  taskEdit: postParamsQueryString('planTaskNew/updTaskList', __PATH.VUE_ICIS_API), // 任务编辑
  deleteInspectionPoint: postParamsQueryString('taskPointRelease/deleteTaskPointRelease', __PATH.VUE_ICIS_API), // 删除巡检点
  imageUpload: postParamsQueryString('file/upload'), // 图片上传
  checkPlan: postParamsQueryString('planTaskNew/toDayTask'), // 校验今天是否生成过任务
  deptInspAnalysis: postParamsQueryString('planTaskNew/departmentTask', __PATH.VUE_ICIS_API), // 科室巡检分析
  ipsmDeptInspAnalysis: postParamsQueryString('planTaskNew/departmentTask', __PATH.VUE_AQ_URL), // 科室巡检分析(运营)
  taskAcceptanceInspection: postParamsQueryString('planTaskNew/taskAcceptanceInspection', __PATH.VUE_ICIS_API), // 任务批量验收
  taskDetailsExport: postParamsQueryString('planTaskNew/getTaskExport', __PATH.VUE_ICIS_API), // 任务详情导出
  getInspectionRecord: postParamsQueryString('planTaskNewApiController/getTaskListByPointCode', __PATH.VUE_ICIS_API), // 获取巡检记录
  saveOperateRecord: postParamsQueryString('operation/record/saveOperationRecords', __PATH.VUE_ICIS_API), // 保存报修记录
  getPlanListByName: postRequest('maintainPlan/getPlanListByName', __PATH.VUE_ICIS_API), // 获取计划列表
  getInspectionResult: postRequest('planTaskStatistics/queryInspectionTemplate', __PATH.VUE_ICIS_API), // 获取巡检结果查询
  queryIsTemplateMultiple: postRequest('planTaskStatistics/queryIsTemplateMultiple ', __PATH.VUE_ICIS_API), // 获取巡检结果查询校验
  queryInspectionPointByPlanId: postRequest('planTaskStatistics/queryInspectionPointByPlanId', __PATH.VUE_ICIS_API), // 获取点位名称列表
  // 问卷
  paper: postParamsQS('questionPvq/create/paper', __PATH.VUE_ICIS_LZP), // 创建问卷
  listPvq: postParamsQS('questionPvq/listPvq', __PATH.VUE_ICIS_LZP), // 问卷列表
  deletePvq: postParamsQS('questionPvq/deletePvq', __PATH.VUE_ICIS_LZP), // 删除问卷
  findPaper: postParamsQS('questionPvq/findPaper', __PATH.VUE_ICIS_LZP), // 查询问卷详情
  saveQuestion: postParamsQS('questionPvq/saveQuestion', __PATH.VUE_ICIS_LZP), // 保存问题
  getPaperQuestions: postParamsQS('questionPvq/getPaperQuestions', __PATH.VUE_ICIS_LZP), // 获取该问卷下的所有问题
  deleteQuestion: postParamsQS('questionPvq/deleteQuestion', __PATH.VUE_ICIS_LZP), // 删除问题
  moveDownQuestion: postParamsQS('questionPvq/moveDownQuestion', __PATH.VUE_ICIS_LZP), // 降序
  moveUpQuestion: postParamsQS('questionPvq/moveUpQuestion', __PATH.VUE_ICIS_LZP), // 升序
  updateQuestion: postParamsQS('questionPvq/updateQuestion', __PATH.VUE_ICIS_LZP), // 问题修改
  updatePvq: postParamsQS('questionPvq/updatePvq', __PATH.VUE_ICIS_LZP), // 修改问卷
  copyPvq: postParamsQS('questionPvq/copyPvq', __PATH.VUE_ICIS_LZP), // 复制问卷
  publishPvq: postParamsQS('questionPvq/publishPvq', __PATH.VUE_ICIS_LZP), // 问卷发布
  getShareUrl: postParamsQS('questionPvq/getShareUrl', __PATH.VUE_ICIS_LZP), // 获取问卷分享地址
  changePvqStatus: postParamsQS('questionPvq/changePvqStatus', __PATH.VUE_ICIS_LZP), // 问卷开始或暂停
  saveAuthRange: postParamsQS('questionPvq/saveAuthRange', __PATH.VUE_ICIS_LZP), //
  getAuthRange: postRequest('questionPvq/getAuthRange', __PATH.VUE_ICIS_LZP), // 获取问卷收集范围设置
  getCrossQuestion: postParamsQS('answerReport/getCrossQuestion', __PATH.VUE_ICIS_LZP), // 查询交叉分析的问题
  saveAnswer: postParamsQS('questionPvq/saveAnswer', __PATH.VUE_ICIS_LZP), // 微信端回答问题的
  getSingle: postParamsQS('answerReport/getSingle', __PATH.VUE_ICIS_LZP), // 微获取单题分析
  crossList: postParamsQS('answerReport/crossList', __PATH.VUE_ICIS_LZP), // 获取交叉分析统计
  crossTotalCharts: postParamsQS('answerReport/crossTotalCharts', __PATH.VUE_ICIS_LZP), // 获取交叉分析汇总统计报表数据
  crossColCharts: postParamsQS('answerReport/crossColCharts', __PATH.VUE_ICIS_LZP), // 获取交叉分析横向统计报表数据
  // -----
  search: postParamsQS('questionPvq/recovery/search', __PATH.VUE_ICIS_LZP), // 问卷回收列表
  getDepartmentCost: postParamsQS('iomsWebReport.do?getDeptCostReport'), // 科室维修成本报表
  getCostStatistical: postParamsQS('iomsWebReport.do?getConsumablesReport'), // 维修耗材消耗统计
  getMaintenanceLocaltion: postParamsQS('iomsWebReport.do?getRegionReport'), // 维修区域报表
  getWorkersTasks: postParamsQS('iomsWebReport.do?getWorkerServices'), // 工人任务量
  getTeamTasks: postParamsQS('iomsWebReport.do?getReportByTeamService'), // 班组工作量
  getMaintenanceMatters: postParamsQS('iomsWebReport.do?getServiceItemReport'), // 维修事项报表
  getServiceItemReportNew: postParamsQS('iomsWebReport.do?getServiceItemReportNew'), // 维修事项报表新
  getServiceItemPie: postParamsQS('iomsWebReport.do?getServiceItemPie'), // 维修事项报表统计表
  getMaintenanceCount: postParamsQS('iomsWebReport.do?getNewMaintenanceCount'), // 科室报修量报表
  getIntegratedReport: postParamsQS('iomsWebReport.do?getIntegratedReport'), // 综合报表
  getIntegratedReportDegree: postParamsQS('iomsWebReport.do?getIntegratedReportDegree'), // 综合报表满意度
  getWorkOrderDataList: getQueryQS('iHCRSStatisticsController/data', __PATH.VUE_IOMS_API), // 获取工单列表
  getCallCenterData: getQueryQS('iHCRSOperOrderController/callCenterData', __PATH.VUE_IOMS_API), // 获取超时工单列表
  getReckonCount: getQueryQS('iHCRSStatisticsController/reckonCount', __PATH.VUE_IOMS_API), // 获取维修统计
  getReckonCount2: getQueryQS('iHCRSStatisticsController/reckonCount2', __PATH.VUE_IOMS_API), // 获取维修统计
  getAllRepairPersonName: postParamsQS('iomsWebReport.do?getAllRepairPersonName'), // 查询所有申报人
  billingRepairCount: postParamsQS('iomsWebReport.do?billingRepairCount'), // 开单统计报表
  repeatRepairCount: postParamsQS('iomsWebReport.do?repeatRepairCount'), // 重复报修统计报表
  getTransportStatistics: getQueryQS('iHCRSStatisticsController/getTransportStatistics', __PATH.VUE_IOMS_API), // 获取运输统计
  getComprehensiveWorkOrderInfo: postQueryQS('iHCRSStatisticsController/getComprehensiveWorkOrderInfo', __PATH.VUE_IOMS_API), // 获取本日超时工单-本日总工单-后勤工单投诉
  getConsumableMaterialTotalExpenses: postQueryQS('iHCRSStatisticsController/getConsumableMaterialTotalExpenses', __PATH.VUE_IOMS_API), // 获取维修耗材总费用
  getServiceWorkOrderStatistics: postQueryQS('iHCRSStatisticsController/getServiceWorkOrderStatistics', __PATH.VUE_IOMS_API), // 服务工单类型统计
  getWorkOrderNumByWeek: postQueryQS('iHCRSStatisticsController/getWorkOrderNumByWeek', __PATH.VUE_IOMS_API), // 获取年度工单趋势
  getTemplateList: getQueryQS('newReportExport/data', __PATH.VUE_IOMS_API), // 获取模板列表
  downLoadTemplate: getQueryQS('newReportExport/downloadFile', __PATH.VUE_IOMS_API), // 下载模板
  renameTemplate: postQueryQS('newReportExport/rename', __PATH.VUE_IOMS_API), // 修改模板名称
  delTemplate: postQueryQS('newReportExport/del', __PATH.VUE_IOMS_API), // 删除模板
  importTemplate: postQueryQS('newReportExport/import', __PATH.VUE_IOMS_API), // 导入模板
  getPersonDeptTransportStatistics: postQueryQS('iHCRSStatisticsController/getPersonDeptTransportStatistics', __PATH.VUE_IOMS_API), // 数据明细-人员-科室
  getTransportTypeStatistics: postQueryQS('iHCRSStatisticsController/getTransportTypeStatistics', __PATH.VUE_IOMS_API), // 数据明细-人员-科室
  getTimeTransportStatistics: postQueryQS('iHCRSStatisticsController/getTimeTransportStatistics', __PATH.VUE_IOMS_API), // 时间分析
  onePersonTaskQuantity: postParamsQS('planTaskNew/onePersonTaskQuantity', __PATH.VUE_ICIS_API), // 任务完成率
  ipsmTaskQuantity: postParamsQS('planTaskNew/onePersonTaskQuantity', __PATH.VUE_AQ_URL), // 任务完成率（双预防）
  getDailyTaskList: postParamsQS('planTaskNew/getDailyTaskList', __PATH.VUE_ICIS_API), // 近10日任务执行情况
  departmentTask: postParamsQS('planTaskNew/departmentTask', __PATH.VUE_ICIS_API), // 部门任务执行分析
  taskPointList: postParamsQS('planTaskNew/taskPointList', __PATH.VUE_ICIS_API), // 巡检点分析
  getPercentOfPassList: postParamsQS('planTaskNew/getPercentOfPassList', __PATH.VUE_ICIS_API), // 巡检合格率
  getTaskPointPageMap: postParamsQS('planTaskNew/getTaskPointPageMap', __PATH.VUE_ICIS_API), // 跳转巡检点列表
  getOlgTransportItem: postParamsQS('iHCRSStatisticsController/getOlgTransportItem', __PATH.VUE_IOMS_API), // 运送服务事项
  getDictValue2: postParamsQS('appDisOlgTask.do?getEntryOrdersReason', __PATH.VUE_IOMS_API), // 挂单原因
  // 字典
  saveTypeDict: postQueryQS('questionTypeDict/saveTypeDict', __PATH.VUE_ICIS_LZP), // 模板字典新增
  checkDictNameExist: postQueryQS('questionTypeDict/checkDictNameExist', __PATH.VUE_ICIS_LZP), // 新增时候检查该字典名称是否存在
  delTypeDict: postParamsQS('questionTypeDict/delTypeDict', __PATH.VUE_ICIS_LZP), // 删除一个问卷类型字典
  queryTypeDict: postParamsQS('questionTypeDict/queryTypeDict', __PATH.VUE_ICIS_LZP), // 查询问卷类型字典列表
  changeTypeDict: postQueryQS('questionTypeDict/changeTypeDict', __PATH.VUE_ICIS_LZP), // 修改问卷类型字典
  queryOneResult: postQueryQS('questionTypeDict/queryOneResult', __PATH.VUE_ICIS_LZP), // 根据id查询问卷类型字典
  queryDictTypeTree: postRequest('questionTypeDict/queryDictTypeTree', __PATH.VUE_ICIS_LZP), // 查询模板列表树状结构
  // ----------------------------运行监测--------------------------------
  GetEntityMenuList: postRequest('entityMenu/getEntityMenuList', __PATH.VUE_IEMC_API), // 获取菜单
  GetAirPoliceMenuList: postRequest('alarm/record/airPoliceMenuList', __PATH.VUE_WARN_API), // 获取报警设备列表
  setEntityMenu: postRequest('entityMenu/setEntityMenu', __PATH.VUE_IEMC_API), // 新增菜单
  updateEntityMenu: postRequest('entityMenu/updateEntityMenu', __PATH.VUE_IEMC_API), // 修改菜单
  delEntityMenu: postRequest('entityMenu/delEntityMenu', __PATH.VUE_IEMC_API), // 删除菜单
  updateLightingGroup: postFormData('surveyAndParameter/updateLightingGroup', __PATH.VUE_IEMC_API), // 分组划分
  GetParamInfoByProject: getRequest('surveyAndParameter/getParamInfoByProject', __PATH.VUE_IEMC_API), // 获取所有参数
  getRealMonitoringList: postRequest('realMonitoring/selectRealMonitoringList', __PATH.VUE_IEMC_API), // 获取监测项列表
  getRealMonitoringListOld: postRequest('realMonitoring/getRealMonitoringList', __PATH.VUE_IEMC_ELEVATOR_API), // 获取监测项列表
  GetRealEnvMonitoringList: postRequest('realMonitoring/getRealEnvMonitoringList', __PATH.VUE_IEMC_API), // 获取监测项列表
  GetAirRunTime: postRequest('airCondition/airRunTime', __PATH.VUE_IEMC_API), // 获取运行时长统计
  GetAirRunRate: postRequest('airCondition/airRunRate', __PATH.VUE_IEMC_API), // 获取运行率排行
  GetAirBreakdown: postRequest('airCondition/airBreakdown', __PATH.VUE_IEMC_API), // 获取故障统计
  GetAirOffLine: postRequest('airCondition/airOffLine', __PATH.VUE_IEMC_API), // 获取离线统计排行
  GetAirRunPolice: postRequest('alarm/record/airRunPolice', __PATH.VUE_WARN_API), // 获取报警统计统计
  GetDetectionItemAlarmList: postRequest('alarm/record/getPoliceHistoryBySurveyCode', __PATH.VUE_WARN_API), // 获取检测项报警列表
  GetDetectionItemAlarmDetails: getQueryQS('alarm/record/getLimAlarmInfoAndParamInfo', __PATH.VUE_WARN_API), // 获取检测项报警详情
  GetRealMonitoringListSecurity: postRequest('realMonitoring/getRealMonitoringListSecurity', __PATH.VUE_IEMC_API), // 获取安防监测项列表
  GetSecurityOtherSysList: postRequest('realMonitoring/getSecurityOtherSysList', __PATH.VUE_IEMC_API), // 获取安防监测项列表
  getBoilerScale: postRequest('realMonitoring/getBoilerScale', __PATH.VUE_IEMC_API), // 获取锅炉运行监测
  createScan: postRequest('wartime/queryAlarmAffirmQrCodeBase64', __PATH.VUE_WARN_API), // 获取锅炉运行监测
  // 基础档案管理
  unitList: postRequest('unitManager/unit-manager/selectByPage'), // 单位列表
  valveTypeList: postRequest('dictionary/sys-value-dict/selectByList'), // 字典-通用接口
  getUnitById: getRequest('unitManager/unit-manager/selectOne'), // 根据单位ID获取详情
  uploadFlie: postFile('file/uploadOne', __PATH.VUE_MINIO_API), // 图片文件上传-通用接口
  addUnit: postRequest('unitManager/unit-manager/insertUnitManager'), // 单位信息新增
  updataUnit: put('unitManager/unit-manager/updateUnitManager'), // 单位信息修改
  deleteUnit: deleteFn('unitManager/unit-manager/delete'), // 单位信息删除
  departList: postRequest('departmentManager/department-manager/selectByPage'), // 部门列表（分页）
  exportDepetList: downFile('departmentManager/department-manager/exportDepartmentList'), // 筛选导出单位信息列表
  getDepartById: getRequest('departmentManager/department-manager/selectOne'), // 根据部门ID获取详情
  getUnitList: getRequest('unitManager/unit-manager/getSelected'), // 单位下拉列表
  getDeptList: getRequest('departmentManager/department-manager/getSelectedDept'), // 部门下拉列表
  getPostMemberListByPage: postRequest('hospitalStaff/hospital-staff/list'), // 岗位-成员分页列表
  getSpaceTree: getRequest('space/structure/spaceTree'), // 空间Tree(5级)
  staffListByPage: postRequest('hospitalStaff/hospital-staff/list'), // 人员列表-分页
  selectByList: postRequest('postManager/post-manager/selectByList'), // 岗位列表
  getStaffDetail: getRequest('hospitalStaff/hospital-staff/getUpdateDetails'), // 根据ID获取人员详情
  getNationList: getRequest('hospitalStaff/hospital-staff/getNationDetails'), // 民族列表
  jobManager: postRequest('jobManager/job-manager/selectByPage'), // 职务列表
  deleteStaff: deleteFn('/hospitalStaff/hospital-staff/delete'), // 删除人员信息
  // ----------空间
  getFloorHaveModel: getRequest('/space/model/getFloorHaveModel'), // 获取有模型的楼层列表
  roomList: postRequest('/space/model/searchSimpleRoomInfo'), // 根据楼层code获取关联空间台账的房间列表
  spaceTypeListByModelCode: postRequest('/space/model/spaceFunctionType'), // 通过楼层code获得已设置空间功能的类型列表
  getspaceStatus: postRequest('/space/model/spaceStatus'), // 获取有模型的使用状态
  getspaceDept: postRequest('/space/model/spaceDept'), // 获取有模型的归属部门
  updateModelMsg: put('/space/model/update'), // 更新模型关联空间详情信息
  getMsgByModelCode: getRequest('/space/model/lookUpByModelCode'), // 获取房间模型关联的空间台账信息
  spaceTreeList: getRequest('/space/structure/structureTree'), // 建筑结构列表树
  getSpacelist: postRequest('/space/spaceInfo/list'), // 空间信息（分页）
  getPageList: postRequest('/space/modifyHistory/pageList'), // 空间历史记录（分页）
  spacePageList: postRequest('/space/modifyHistory/pageList'),
  getUploadOne: postFormData('/file/uploadOne', __PATH.VUE_SPACE_FILE), // 基础信息图片文件上传-通用接口
  getFilesByIds: getRequest('/file/getFilesByIds', __PATH.VUE_SPACE_FILE),
  getSettingItemByKey: getRequest('/setting/getSettingItemByKey', __PATH.VUE_SPACE_API),
  deleteStructure: deleteFn('/space/structure/deleteStructure'), // 空间结构删除
  getArea: getRequest('/space/structure/getArea'), // 获取医院所属院区信息
  getBuildByAreaID: getRequest('/space/structure/lookUpBuildByArea'), // 根据区域ID获取楼宇列表
  getEmptyBuild: getRequest('/space/structure/getEmptyBuild'), // 楼层字典列表
  addBuild: postRequest('/space/structure/addBuild'), // 新增楼宇
  updateBuild: put('/space/structure/updateBuild'), // 修改楼宇
  addArea: postRequest('/space/structure/addArea'), // 新增区域
  updateArea: put('/space/structure/updateArea'), // 修改区域
  getSpaceDetail: getRequest('/space/spaceInfo/lookUpById'), // 查看空间详情信息
  updateSpace: put('/space/spaceInfo/update'), // 修改空间信息
  addSpace: postRequest('/space/spaceInfo/add'), // 新增空间信息
  deleteSpace: deleteFn('/space/spaceInfo/delete'), // 删除空间信息
  bindSpace: put('/space/spaceInfo/bind'), // 删除空间信息
  getRoomCountAndArea: getRequest('space/client/getRoomCountAndArea', __PATH.VUE_SPACE_API), // 获取空间信息房间数据
  getRoomCountByPid: getRequest('/space/client/getRoomCountByPid'), // 获取建筑空间分布
  getRoomCountAndAreaPageList: getRequest('space/client/getRoomCountAndAreaPageList', __PATH.VUE_SPACE_API),
  spaceList: postRequest('space/client/list', __PATH.VUE_SPACE_API),
  getRoomCountList: getRequest('space/client/getRoomCountList', __PATH.VUE_SPACE_API), // 获取空间信息列表
  getRoomCountListIgnoreVersion: getRequest('space/client/getRoomCountListIgnoreVersion', __PATH.VUE_SPACE_API), // 通过部门或者空间用途并忽略version来获得房间数量的列表|wcl
  getRoomCountAndAreaByStatus: getRequest('space/client/getRoomCountAndAreaByStatus', __PATH.VUE_SPACE_API), // 通过空间的模型编码和使用状态获得总面积总房间和空闲房间等|wcl
  getStateStatistics: postRequest('logistics/dangerController/getStateStatistics', __PATH.VUE_AQ_URL), // 隐患类型分析
  getHiddenDangerTypeAnalysis: postRequest('logistics/dangerController/getHiddenDangerTypeAnalysis', __PATH.VUE_AQ_URL), // 隐患等级分析
  selectHiddenDangerList: postRequest('logistics/dangerController/selectHiddenDangerList', __PATH.VUE_AQ_URL), // 隐患列表
  getHistoryFollow: postRequest('logistics/dangerController/getHistoryFollowInfo', __PATH.VUE_AQ_URL), // 隐患详情
  getRiskLevelStatistics: postRequest('logistics/riskController/getRiskLevelStatistics', __PATH.VUE_AQ_URL), // 查询风险等级
  getRiskDeptStatistics: postRequest('logistics/riskController/getRiskTypeStatistics', __PATH.VUE_AQ_URL), // 查询风险类型分析
  getRiskPageList: postRequest('logistics/riskController/getRiskPageList', __PATH.VUE_AQ_URL), // 查询风险列表
  getRiskDetail: postRequest('logistics/riskController/getRiskDetail', __PATH.VUE_AQ_URL), // 风险详情,
  getRiskDeptAnalysis: postRequest('logistics/riskController/getRiskDeptStatistics', __PATH.VUE_AQ_URL), // 风险部门分布
  reckonCount: postParamsQS('iHCRSStatisticsController/reckonCount', __PATH.VUE_IOMS_API),
  getWorkOrder: postParamsQS('iHCRSStatisticsController/getWorkOrderTypeStatisticsBySpaceRuiAn', __PATH.VUE_IOMS_API),
  getWorkOrderTypeStatisticsBySpaceRuiAn: postParamsQS('iHCRSStatisticsController/getWorkOrderCountStatisticsBySpaceRuiAn', __PATH.VUE_IOMS_API),
  getWorkOrderListBySpaceRuiAn: postParamsQS('iHCRSStatisticsController/getWorkOrderListBySpaceRuiAn', __PATH.VUE_IOMS_API),
  getRuiAnTypeAnalysisInfo: postParamsQS('iHCRSStatisticsController/getRuiAnTypeAnalysisInfo', __PATH.VUE_APP_IMWS_API),
  getMedicalTypeWeekTrendInfo: postParamsQS('iHCRSStatisticsController/getMedicalTypeWeekTrendInfo', __PATH.VUE_APP_IMWS_API),
  getDepartMedicalWasteList: postParamsQS('iHCRSStatisticsController/getDepartMedicalWasteList', __PATH.VUE_APP_IMWS_API),
  gatherRetrospect: postParamsQS('iHCRSStatisticsController/gatherRetrospect', __PATH.VUE_APP_IMWS_API),
  getAreaDepartmentMedicalWasteInfo: postParamsQS('iHCRSStatisticsController/getAreaDepartmentMedicalWasteInfo', __PATH.VUE_APP_IMWS_API),
  getAssetOverview: postParamsQS('asset/assetDetails/getAssetOverview', __PATH.VUE_ICIS_API), // 资产总览
  getAssetsServiceLife: postParamsQS('asset/assetDetails/getAssetsServiceLife', __PATH.VUE_ICIS_API), // 资产使用年限
  getAssetClassification: postParamsQS('asset/assetDetails/getAssetClassification', __PATH.VUE_ICIS_API), // 资产分类
  getAssetServiceList: postParamsQS('asset/assetDetails/getAssetServiceList', __PATH.VUE_ICIS_API), // 资产服务列表
  getRiskStatusCount: postParamsQS('riskWorkOrder/workOrderCount', __PATH.VUE_AQ_URL), // 风险等级统计
  getRiskTypeAnalysis: postParamsQS('statisticsController/getRiskStatsByType', __PATH.VUE_AQ_URL), // 风险类型统计
  // ---------------------
  getIpsmLoginInfo: postParamsQS('userLoginController/ihcrsUserLogin', __PATH.VUE_AQ_URL), // 获取登录信息及验证角色权限
  GetAirViewDetail: postRequest('airCondition/airViewDetail', __PATH.VUE_IEMC_API), // 获取统计详情
  GetNewAirViewDetail: postRequest('airCondition/newAirViewDetail', __PATH.VUE_IEMC_API), // 获取统计详情
  GetAirRunPoliceDetail: postRequest('alarm/record/airRunPoliceDetail', __PATH.VUE_WARN_API), // 获取空调报警详情
  // 安全--隐患管理
  ipsmGetHiddenPoolList: postQuartersSecurity('safetyManager/hospitalQuestion/selectHiddenPoolList', __PATH.VUE_AQ_URL), // 审核列表
  ipsmGetDictList: postQuartersSecurity('sysDictData/findList', __PATH.VUE_AQ_URL), // 隐患等级
  ipsmGetHiddenClassifyList: postQuartersSecurity('safetyManager/common/getDetailType', __PATH.VUE_AQ_URL), // 隐患分类
  ipsmGetResponsibleDepartment: postQuartersSecurity('safetyManager/common/selectDepartList', __PATH.VUE_AQ_URL), // 隐患上报-获取责任部门
  ipsmClaimQuestion: postQuartersSecurity('safetyManager/hospitalQuestion/claimQuestion', __PATH.VUE_AQ_URL), // 隐患池-认领
  ipsmHiddenPoolTransfer: postQuartersSecurity('safetyManager/hospitalQuestion/appointQuestion', __PATH.VUE_AQ_URL), // 隐患池-转派
  ipsmHiddenExportFile: downFileAQ('safetyManager/hospitalQuestion/exportHiddenPoolList', __PATH.VUE_AQ_URL), // 导出
  ipsmGetHiddenInfoById: postQuartersSecurity('safetyManager/hospitalQuestion/getQuestionInfo', __PATH.VUE_AQ_URL), // 根据id查隐患池详情
  ipsmGetAuditsList: postQuartersSecurity('safetyManager/hospitalQuestion/selectAuditList', __PATH.VUE_AQ_URL), // 审核管理-获取审核列表
  ipsmAuditQuestion: postQuartersSecurity('safetyManager/hospitalQuestion/auditQuestion', __PATH.VUE_AQ_URL), // 审核管理-审核
  ipsmGetHistoryFollowInfo: postQuartersSecurity('safetyManager/hospitalQuestion/getHistoryFollowInfo', __PATH.VUE_AQ_URL), // 隐患管理详情
  ipsmGetHiddenGridList: postQuartersSecurity('safetyManager/common/getGridList', __PATH.VUE_AQ_URL), // 隐患上报-获取隐患区域
  ipsmGetSelectTimeOut: postQuartersSecurity('safetyManager/common/selectTimeOut', __PATH.VUE_AQ_URL), // 获取默认时间
  ipsmReportSaved: postQuartersSecurity('safetyManager/hospitalQuestion/saveQuestion', __PATH.VUE_AQ_URL), // 隐患上报-保存
  ipsmGetHiddenManageList: postQuartersSecurity('safetyManager/hospitalQuestion/selectQuestionList', __PATH.VUE_AQ_URL), // 获取隐患管理列表
  ipsmGetGridList: postParamsQSSecurity('riskController/getGridList', __PATH.VUE_AQ_URL), // 获取风险点位置
  ipsmGetDeptPersonById: postQuartersSecurity('safetyManager/common/getUseListByDeptId', __PATH.VUE_AQ_URL), // 根据科室id获取人员
  ipsmRectifyQuestion: postQuartersSecurity('safetyManager/hospitalQuestion/rectifyQuestion', __PATH.VUE_AQ_URL), // 隐患整改
  ipsmCreditQuestion: postQuartersSecurity('safetyManager/hospitalQuestion/creditQuestion', __PATH.VUE_AQ_URL), // 隐患挂帐
  // 安全--考核管理
  ipsmGetColumnName: postParamsQSSecurity('penaltiesManager/getColumnName', __PATH.VUE_AQ_URL), // 获取奖惩制度列表表头
  ipsmUserPenaltiesList: postParamsQSSecurity('penaltiesManager/userPenaltiesList', __PATH.VUE_AQ_URL), // 奖惩管理列表
  ipsmGetPenaltiesManagerExportFields: getSecurity('penaltiesManager/getExportFields', __PATH.VUE_AQ_URL), // 获取奖惩导出字段
  ipsmPenaltiesManagerExportExcel: getSecurityType('penaltiesManager/exportExcel', __PATH.VUE_AQ_URL), // 导出
  // 安全--消息管理
  ipsmGetNoticeType: postParamsQSSecurity('notice/getNoticeType', __PATH.VUE_AQ_URL), // 获取公告类别
  ipsmGetDictValue: postParamsQSSecurity('riskController/getDictValue', __PATH.VUE_AQ_URL), // 获取公告状态
  ipsmGetNoticeList: postParamsQSSecurity('notice/getNoticeList', __PATH.VUE_AQ_URL), // 获取公告列表
  ipsmDelNotice: postParamsQSSecurity('notice/delNotice', __PATH.VUE_AQ_URL), // 删除公告
  ipsmIssueNotice: postParamsQSSecurity('notice/issueNotice', __PATH.VUE_AQ_URL), // 发布公告
  ipsmGetNoticeScopeByType: postParamsQSSecurity('notice/getNoticeScopeByType', __PATH.VUE_AQ_URL), // 根据公告类别获取公告范围
  ipsmCreateNotice: postParamsQSSecurity('notice/createNotice', __PATH.VUE_AQ_URL), // 新建公告
  ipsmUpdateNotice: postParamsQSSecurity('notice/updateNotice', __PATH.VUE_AQ_URL), // 修改公告
  ipsmGetNoticeDetails: postParamsQSSecurity('notice/getNoticeDetails', __PATH.VUE_AQ_URL), // 获取公告详情
  ipsmGetWorkNoticeRecordList: postParamsQSSecurity('workNoticeRecordController/getWorkNoticeRecordList', __PATH.VUE_AQ_URL), // 获取知会列表
  ipsmRiskWorkGetControlTeamUserList: postParamsQSSecurity('riskWorkOrder/getControlTeamUserList', __PATH.VUE_AQ_URL), // 获取管控小组人员列表（pc端指派人员使用）
  // 安全--知识库管理
  ipsmListData: postParamsQSSecurity('repository/listData', __PATH.VUE_AQ_URL), // 文档列表
  ipsmRepositoryDelete: postParamsQSSecurity('repository/delete', __PATH.VUE_AQ_URL), // 文档删除
  // 安全--数据统计
  ipsmGetControlGroupInfoList: postQuartersSecurity('controlGroupInfo/getControlGroupInfoList', __PATH.VUE_AQ_URL), // 获取安全管控组织列表
  // ipsmGetHiddenManageList: postQuartersSecurity('safetyManager/hospitalQuestion/selectQuestionList', __PATH.VUE_AQ_URL), // 获取隐患管理列表
  ipsmGetRiskAnalysis: postQuartersSecurity('safetyManager/hospitalQuestion/selectQuestionAnalysis', __PATH.VUE_AQ_URL), // 获取隐患分析数据
  ipsmGetTasks: postParamsQSSecurity('planTaskNew/departmentTask', __PATH.VUE_AQ_URL), // 获取巡检统计分析
  ipsmFindTaskList: postParamsSecurity('plan/getWorkProgress', __PATH.VUE_AQ_URL), // 任务管理列表
  // 安全--系统设置
  ipsmUserRoleGetRoleList: postParamsQSSecurity('userRolesController/roleList', __PATH.VUE_AQ_URL), // 获取用户角色列表
  ipsmGetUserListByWebRoleCode: postQuartersSecurity('controlTeamUser/getUserListByWebRoleCode', __PATH.VUE_AQ_URL), // 获取角色下的用户
  ipsmUpdateStatus: postParamsQSSecurity('userRolesController/updateStatus', __PATH.VUE_AQ_URL), // 启用禁用角色
  ipsmDelRole: postParamsQSSecurity('userRolesController/delRole', __PATH.VUE_AQ_URL), // 删除角色
  ipsmInsert: postParamsQSSecurity('userRolesController/insert', __PATH.VUE_AQ_URL), // 新增角色信息
  ipsmUpdate: postParamsQSSecurity('userRolesController/update', __PATH.VUE_AQ_URL), // 修改角色信息
  ipsmGetRoleDetail: postParamsQSSecurity('userRolesController/getRoleDetail', __PATH.VUE_AQ_URL), // 查看角色信息详情
  ipsmUserMenuControllerGetMenuList: postParamsQSSecurity('userMenuController/getMenuList', __PATH.VUE_AQ_URL), // 根据获取菜单列表.若角色没有关联菜单,则返回所有菜单
  ipsmAddAssociated: postParamsQSSecurity('userMenuController/addAssociated', __PATH.VUE_AQ_URL), // 菜单角色关联
  ipsmGetStaffListByGroup: postParamsQSSecurity('controlGroup/getStaffListByGroup', __PATH.VUE_AQ_URL), // 获取管控小组下的职工
  ipsmUpdatePermission: postParamsQSSecurity('controlGroup/updatePermission', __PATH.VUE_AQ_URL), // 修改职工权限
  ipsmRiskJudgeFindList: postParamsQSSecurity('riskJudgeController/findList', __PATH.VUE_AQ_URL), // 获取研判方法信息
  ipsmUpdateRiskJudge: postParamsQSSecurity('riskJudgeController/updateRiskJudge', __PATH.VUE_AQ_URL), // 更新研判信息
  ipsmPenaltiesList: postParamsQSSecurity('penalties/penaltiesList', __PATH.VUE_AQ_URL), // 获取奖惩配置列表
  ipsmUpdatePenalties: postParamsQSSecurity('penalties/updatePenalties', __PATH.VUE_AQ_URL), // 奖惩配置修改
  ipsmFindDictionaryType: postParamsQSSecurity('sysDictType/listData', __PATH.VUE_AQ_URL), // 获取字典类型
  ipsmFindDictionaryTableList: postParamsQSSecurity('sysDictData/listData', __PATH.VUE_AQ_URL), // 获取字典列表右侧表格
  ipsmSaveDictionary: postParamsQSSecurity('sysDictData/save', __PATH.VUE_AQ_URL), // 新增字典
  ipsmUpdateDictionary: postParamsQSSecurity('sysDictData/update', __PATH.VUE_AQ_URL), // 编辑字典
  ipsmDeleteDictionary: postParamsQSSecurity('sysDictData/delete', __PATH.VUE_AQ_URL), // 删除字典
  ipsmDictionaryDetails: postParamsQSSecurity('sysDictData/get', __PATH.VUE_AQ_URL), // 字典详情
  ipsmGetAccidentTypeList: postParamsQSSecurity('accidentType/getAccidentTypeList', __PATH.VUE_AQ_URL), // 获取事故类型列表
  ipsmGetAccidentTypeDetail: postParamsQSSecurity('accidentType/getAccidentTypeDetail', __PATH.VUE_AQ_URL), // 查看事故类型详情
  ipsmSaveAccidentType: postSaveSecurity('accidentType/saveAccidentType', __PATH.VUE_AQ_URL), // 新增/修改事故类型字典项
  ipsmDeleteAccidentType: postParamsQSSecurity('accidentType/deleteAccidentType', __PATH.VUE_AQ_URL), // 删除事故类型字典值
  ipsmEquipmentListData: postQuartersSecurity('devicetype/equipmentListData', __PATH.VUE_AQ_URL), // 获取类别
  ipsmHandleClassify: postParamsQSSecurity('devicetype/save', __PATH.VUE_AQ_URL), // 字典分类录入
  ipsmDeleteEquipment: postParamsQSSecurity('devicetype/deleteEquipment', __PATH.VUE_AQ_URL), // 字典删除
  ipsmGetEquipmentList: postQuartersSecurity('equipmentParameter/equipmentList', __PATH.VUE_AQ_URL), // 根据设备小类查询 设备参数
  ipsmGetClassifyDictList: postQuartersSecurity('safetyManager/safetyQuestionType/findList', __PATH.VUE_AQ_URL), // 获取隐患分类字典列表
  ipsmDeleteClassifyDictList: postQuartersSecurity('safetyManager/safetyQuestionType/delete', __PATH.VUE_AQ_URL), // 删除隐患分类字典列表
  ipsmGetClassifyInfoById: postQuartersSecurity('safetyManager/safetyQuestionType/getQuestionType', __PATH.VUE_AQ_URL), // 通过id查询隐患分类字典
  ipsmSaveClassifyDictList: postQuartersSecurity('safetyManager/safetyQuestionType/save', __PATH.VUE_AQ_URL), // 保存隐患分类字典列表
  ipsmUpdateClassifyDictList: postQuartersSecurity('safetyManager/safetyQuestionType/update', __PATH.VUE_AQ_URL), // 修改隐患分类字典列表
  // ipsmGetSpaceGridTree: postQuartersSecurity('space/getStructureTree', __PATH.VUE_AQ_URL), // 获取空间网格管理左侧树
  ipsmGetRiskFourColorChartMangement: postQuartersSecurity('space/getCascadeHospitalSpaceInfo', __PATH.VUE_AQ_URL), // 风险四色图管理右侧
  ipsmImageUpload: postQuartersSecurity('space/updatePictureUrl', __PATH.VUE_AQ_URL), // 风险四色图上传
  ipsmGetConfigurationListData: postQuartersSecurity('hospital/configuration/hospitalConfiguration/listData', __PATH.VUE_AQ_URL), // 查看参数配置
  ipsmSetConfigurationSave: postRequestCS('hospital/configuration/hospitalConfiguration/save', __PATH.VUE_AQ_URL), // 保存参数配置
  ipsmGetTimeOutConfig: postQuartersSecurity('safetyManager/hospitalConfiguration/selectTimeOutConfig', __PATH.VUE_AQ_URL), // 查询隐患超时配置列表
  ipsmSaveTimeOut: postQuartersSecurity('safetyManager/hospitalConfiguration/saveTimeOut', __PATH.VUE_AQ_URL), // 保存隐患超时配置
  ipsmGetRiskEvaluation: postQuartersSecurity('sysDictType/getRiskEvaluation', __PATH.VUE_AQ_URL), // 查询风险评价配置
  ipsmSaveRiskEvaluation: postQuartersSecurity('sysDictType/saveRiskEvaluation', __PATH.VUE_AQ_URL), // 保存风险评价配置
  ipsmGetControlTeamUserList: postQuartersSecurity('controlTeamUser/getControlTeamUserList', __PATH.VUE_AQ_URL), // 获取安全人员列表
  // ipsmUpdateControlTeamUser: postQuartersSecurity('controlTeamUser/updateControlTeamUser', __PATH.VUE_AQ_URL), // 更新安全人员信息接口和修改权限接口
  // 安全--巡检管理
  findDictionaryTableList: postParamsSecurity('sysDictData/listData', __PATH.VUE_AQ_URL), // 获取字典列表右侧表格
  findTaskBookList: postParamsSecurity('maintainProjectController/findPage', __PATH.VUE_AQ_URL), // 任务书列表查询
  ipsmAddTaskBookProject: postParamsSecurity('maintainProjectController/addMaintainProject', __PATH.VUE_AQ_URL), // 新增任务书管理
  deleteTaskBookList: postParamsSecurity('maintainProjectController/deleteProjectAndDetails', __PATH.VUE_AQ_URL), // 删除任务书
  ipsmGetDictValueList: postParamsSecurity('dictUtils/getDictList', __PATH.VUE_AQ_URL), // 字典选项：巡检选项
  ipsmGetTaskBookDetails: postParamsSecurity('maintainProjectController/findMaintainProjectById', __PATH.VUE_AQ_URL), // 任务书管理查询详情
  deleteMaintainPlan: postParamsSecurity('maintainPlan/deleteMaintainPlan', __PATH.VUE_AQ_URL), // 删除计划
  updateMaintainPlanUseState: postParamsSecurity('maintainPlan/updateMaintainPlanUseState', __PATH.VUE_AQ_URL), // 修改计划启用/停用状态
  findPageLists: postParamsSecurity('maintainPlan/findPage', __PATH.VUE_AQ_URL), // 计划列表
  findTaskPointList: postParamsSecurity('taskPoint/listData', __PATH.VUE_AQ_URL), // 任务点列表查询
  getControlTeamUserList: postQuartersSecurity('controlTeamUser/getControlTeamUserList', __PATH.VUE_AQ_URL), // 获取安全人员列表
  addOrUpdateMaintainPlans: postParamsSecurity('maintainPlan/addOrUpdateMaintainPlan', __PATH.VUE_AQ_URL), // 新增计划
  getMaintainPlanDetail: postParamsSecurity('maintainPlan/getMaintainPlanDetail', __PATH.VUE_AQ_URL),
  findPlanSchedule: postParamsSecurity('planTaskNew/listData', __PATH.VUE_AQ_URL), // 计划进度详情 // 获取计划详情
  findScheduleDetails: postParamsSecurity('taskPointRelease/listData', __PATH.VUE_AQ_URL), // 任务管理-->详情1
  findRecordDetails: postParamsSecurity('taskPointRelease/detail', __PATH.VUE_AQ_URL), // 详情2
  ipsmFindLocationPointList: postParamsSecurity('locationPoint/listData', __PATH.VUE_AQ_URL), // 定位点列表查询
  ipsmDeleteLocationListList: postParamsSecurity('locationPoint/delete', __PATH.VUE_AQ_URL), // 删除定位点
  ipsmGetLocationPointDetails: postParamsSecurity('locationPoint/get', __PATH.VUE_AQ_URL), // 定位点管理查询详情
  ipsmAddLocationPoint: postParamsSecurity('locationPoint/saveOrUpdate', __PATH.VUE_AQ_URL), // 新增/编辑定位点管理
  deleteTaskList: postParamsSecurity('taskPoint/delete', __PATH.VUE_AQ_URL), // 删除定位点
  getTaskPointDetails: postParamsSecurity('taskPoint/get', __PATH.VUE_AQ_URL), // 定位点管理查询详情
  addTaskPoint: postParamsSecurity('taskPoint/saveOrUpdate', __PATH.VUE_AQ_URL), // 新增/编辑任务点管理
  assetsClassListData: post_isp_iaast('assetsClass/listData', __PATH.VUE_APP_IEMS_API), // 分类
  getSupplierList: post_isp_iaast('iaasAssetController/getSupplierList', __PATH.VUE_APP_IEMS_API), // 获取供应商
  findEquipmentList: post_isp_iaast('assetsInfo/listData', __PATH.VUE_APP_IEMS_API), // 设备(资产)列表
  findLocationPointGetBeacon: postParamsSecurity('locationPoint/getBeacon', __PATH.VUE_AQ_URL), // 定位点列表查询(获取外部Beacon)
  getRegionTypeByCompanyCode: postParamsSecurity('maintainPlan/getRegionTypeByCompanyCode', __PATH.VUE_AQ_URL),
  getRegionList: postParamsSecurity('orgManagementController/getRegionList', __PATH.VUE_AQ_URL), // 获取区域树形结构接口
  // 安全--风险管控
  ipsmRiskManageFindList: postParamsQSSecurity('riskManageController/findList', __PATH.VUE_AQ_URL), // 获取风险点列表
  ipsmGetPictureUrls: postParamsQSSecurity('riskManage/getPictureUrls', __PATH.VUE_AQ_URL), // 获取图片集合
  ipsmRiskManageDeleteRisk: postParamsQSSecurity('riskManageController/deleteRisk', __PATH.VUE_AQ_URL), // 删除风险点
  ipsmRiskManageGetRiskDetail: postParamsQSSecurity('riskManageController/getRiskDetail', __PATH.VUE_AQ_URL), // 获取风险详情
  // ipsmRiskJudgeFindList: postParamsQSSecurity('riskJudgeController/findList', __PATH.VUE_AQ_URL), // 获取研判方法信息
  ipsmSaveRiskJudgeRecord: postParamsQSSecurity('riskJudgeRecordController/saveRiskJudgeRecord', __PATH.VUE_AQ_URL), // 保存研判记录
  ipsmQueryQRCode: postParamsQSSecurity('riskManageController/queryQRCode', __PATH.VUE_AQ_URL), // 查看二维码
  ipsmRiskManageGetExportFields: getSecurity('riskManageController/getExportFields', __PATH.VUE_AQ_URL), // 获取要导出的字段名称
  ipsmGetRiskJudgeRecord: postParamsQSSecurity('riskJudgeRecordController/getRiskJudgeRecord', __PATH.VUE_AQ_URL), // 查看研判历史记录
  ipsmRiskManageUpdateRisk: postSaveSecurity('riskManageController/updateRisk', __PATH.VUE_AQ_URL), // 修改风险点信息
  ipsmRiskManageInsertRisk: postSaveSecurity('riskManageController/insertRisk', __PATH.VUE_AQ_URL), // 新增风险点
  ipsmRiskManageRiskUpdateStatus: postParamsQSSecurity('riskManageController/updateStatus', __PATH.VUE_AQ_URL), // 启用禁用风险点
  ipsmCheckRiskInvestigation: postParamsQSSecurity('riskInvestigation/checkRiskInvestigation', __PATH.VUE_AQ_URL), // 查看排查清单是否建立
  ipsmGetRiskInvestigationDetail: postParamsQSSecurity('riskInvestigation/getRiskInvestigationDetail', __PATH.VUE_AQ_URL), // 获取排查清单详情
  ipsmSaveRiskInvestigation: postParamsQSSecurity('riskInvestigation/saveRiskInvestigation', __PATH.VUE_AQ_URL), // 新增风险排查清单
  ipsmUpdateRiskInvestigation: postParamsQSSecurity('riskInvestigation/updateRiskInvestigation', __PATH.VUE_AQ_URL), // 更新风险排查清单
  ipsmGetRiskInvestigationRecord: postParamsQSSecurity('riskInvestigationRecord/getRiskInvestigationRecord', __PATH.VUE_AQ_URL), // 获取排查记录列表
  ipsmGetRiskInvestigationRecordDetail: postParamsQSSecurity('riskInvestigationRecord/getRiskInvestigationRecordDetail', __PATH.VUE_AQ_URL), // 获取排查记录详情
  ipsmRiskManageExportExcel: getSecurityType('riskManageController/exportExcel', __PATH.VUE_AQ_URL), // 获取排查记录详情
  ipsmExportQRCode: getSecurityType('riskManageController/exportQRCode', __PATH.VUE_AQ_URL), // 导出二维码
  ipsmExportWord: getSecurityType('riskManageController/exportWord', __PATH.VUE_AQ_URL), // 导出风险告知卡
  // 安全--安全台账
  ipsmDelControlGroupInfo: postQuartersSecurity('controlGroupInfo/delControlGroupInfo', __PATH.VUE_AQ_URL), // 删除安全管控组织
  ipsmGetControlGroupInfoExportFields: postQuartersSecurity('controlGroupInfo/getExportFields', __PATH.VUE_AQ_URL), // 获取导出字段
  ipsmControlGroupInfoExportExcel: getSecurityType('controlGroupInfo/exportExcel', __PATH.VUE_AQ_URL), // 导出
  ipsmOutsourcedTreeData: postParamsQSSecurity('controlGroupInfo/getHospitalOfficeList', __PATH.VUE_AQ_URL), // 新基础 左侧列表
  ipsmGetStaffListByOfficeOrTeam: postParamsQSSecurity('controlGroupInfo/getStaffListByOfficeOrTeam', __PATH.VUE_AQ_URL), // 新基础 右侧列表
  ipsmGetControlGroupInfo: postQuartersSecurity('controlGroupInfo/getControlGroupInfo', __PATH.VUE_AQ_URL), // 获取安全管控组织信息详情
  ipsmSaveControlGroupInfo: postQuartersSecurity('controlGroupInfo/saveControlGroupInfo', __PATH.VUE_AQ_URL), // 新增或更新安全管控组织
  ipsmGetControlTeamUseExportFields: postQuartersSecurity('controlTeamUser/getExportFields', __PATH.VUE_AQ_URL), // 获取导出字段
  ipsmControlTeamUserExportExcel: getSecurityType('controlTeamUser/exportExcel', __PATH.VUE_AQ_URL), // 导出
  ipsmUpdateControlTeamUser: postQuartersSecurity('controlTeamUser/updateControlTeamUser', __PATH.VUE_AQ_URL), // 更新安全人员信息接口和修改权限接口
  ipsmUpdatePassword: postParamsQSSecurity('controlTeamUser/updatePassword', __PATH.VUE_AQ_URL), // 密码修改和一键重置密码接口
  ipsmAddControlTeamUser: postQuartersSecurity('controlTeamUser/addControlTeamUser', __PATH.VUE_AQ_URL), // 新增安全人员信息
  ipsmDelControlTeamUser: postQuartersSecurity('controlTeamUser/delControlTeamUser', __PATH.VUE_AQ_URL), // 删除安全人员信息
  ipsmGetControlTeamUserInfo: postQuartersSecurity('controlTeamUser/getControlTeamUserInfo', __PATH.VUE_AQ_URL), // 获取安全人员信息
  ipsmGetHospitalInfo: postQuartersSecurity('hospitalInfo/getHospitalInfo', __PATH.VUE_AQ_URL), // 获取医院详情
  ipsmSaveHospital: postQuartersSecurity('hospitalInfo/saveHospital', __PATH.VUE_AQ_URL), // 保存医院信息
  ipsmEmphasisstandingbookList: postParamsQSSecurity('standingBook/listData', __PATH.VUE_AQ_URL), // 系统使用统计
  ipsmRemoveKeyLedge: postQuartersSecurity('standingBook/delete', __PATH.VUE_AQ_URL), // 删除
  ipsmRemoveKeyLedgeFile: postParamsQSSecurity('fileuplocad/delete', __PATH.VUE_AQ_URL), // 删除文件
  ipsmSaveKeyLedge: postParamsQSSecurity('standingBook/save', __PATH.VUE_AQ_URL), // 保存重点台账
  ipsmGetKeyLedgeById: postQuartersSecurity('fileuplocad/fileFistData', __PATH.VUE_AQ_URL), // 根据id查台账信息
  // ipsmGetSpaceGridType: postQuartersSecurity('space/getDictList', __PATH.VUE_AQ_URL), // 获取空间网格功能类型
  ipsmGetSpaceGridType: getSecurityType('web/prDictionaryDetailsD/getSuperiorData', __PATH.VUE_SPACE_API), // 获取空间网格功能类型
  ipsmGetSpaceGridTree: postQuartersSecurity('space/getStructureTree', __PATH.VUE_AQ_URL), // 获取空间网格管理左侧树
  ipsmGetSpaceGridTable: postQuartersSecurity('space/getSpacePageList', __PATH.VUE_AQ_URL), // 获取空间网格右侧表
  // 安全--首页
  ipsmGetTasksAnalysis: postParamsQSSecurity('riskWorkOrder/getWorkOrderStatisticsHomePage', __PATH.VUE_AQ_URL), // 首页日常隐患分析
  ipsmGetWorkOrderStatistics: postParamsQSSecurity('riskManage/getWorkOrderStatistics', __PATH.VUE_AQ_URL), // 隐患整改统计
  ipsmGetRiskStatsByType: postParamsQSSecurity('statisticsController/getRiskStatsByType', __PATH.VUE_AQ_URL), // 风险类型统计
  ipsmWorkOrderCount: postParamsQSSecurity('riskWorkOrder/workOrderCount', __PATH.VUE_AQ_URL), // 工单状态统计(待审核/重大风险和较大风险)
  ipsmGetHiddenDangerTrend: postParamsQSSecurity('riskWorkOrder/getHiddenDangerTrend', __PATH.VUE_AQ_URL), // 获取隐患数量走势图
  ipsmGetHiddenDangerList: postQuartersSecurity('homeQuestion/selectHiddenDangerList', __PATH.VUE_AQ_URL), // 获取隐患实时列表
  ipsmGetFourStatistics: postQuartersSecurity('space/getSpaceRiskQuestionAnalysis', __PATH.VUE_AQ_URL), // 风险四色图统计
  ipsmGetRiskList: postQuartersSecurity('homeQuestion/selectRiskAnalyseList', __PATH.VUE_AQ_URL), // 获取风险列表
  ipsmGetHiddenList: postQuartersSecurity('homeQuestion/selectQuestionAnalyseList', __PATH.VUE_AQ_URL), // 获取隐患列表
  getHospitalQuestionStateAnalysis: postRequest('logistics/dangerController/getHospitalQuestionStateAnalysis', __PATH.VUE_AQ_URL), // 获取隐患总览
  selectDeptDangerDistribution: postRequest('logistics/dangerController/selectDeptDangerDistribution', __PATH.VUE_AQ_URL), // 获取科室隐患分布
  // 统一权限
  // userRoleGetRoleListIpsm: postParamsTY('userRolesController/roleList', __PATH.VUE_AQ_URL), // 获取用户角色列表
  userMenuControllerGetMenuListIpsm: postParamsTY('userMenuController/getMenuList', __PATH.VUE_AQ_URL), // 根据获取菜单列表.若角色没有关联菜单,则返回所有菜单
  addAssociatedIpsm: postParamsTY('userMenuController/addAssociated', __PATH.VUE_AQ_URL), // 菜单角色关联
  getUserListByWebRoleCodeIpsm: postQuartersTY('controlTeamUser/getUserListByWebRoleCode', __PATH.VUE_AQ_URL), // 获取角色下的用户
  delControlTeamUserIpsm: postQuartersTY('controlTeamUser/delControlTeamUser', __PATH.VUE_AQ_URL), // 删除安全人员信息
  getControlTeamUserInfoIpsm: postQuartersTY('controlTeamUser/getControlTeamUserInfo', __PATH.VUE_AQ_URL), // 获取安全人员信息
  addControlTeamUserIpsm: postQuartersTY('controlTeamUser/addControlTeamUser', __PATH.VUE_AQ_URL), // 新增安全人员信息
  updateControlTeamUserIpsm: postQuartersTY('controlTeamUser/updateControlTeamUser', __PATH.VUE_AQ_URL), // 更新安全人员信息接口和修改权限接口
  getControlGroupInfoListIpsm: postQuartersTY('controlGroupInfo/getControlGroupInfoList', __PATH.VUE_AQ_URL), // 获取安全管控组织列表
  userRoleGetRoleListIpsm: postParamsTY('userRolesController/roleList', __PATH.VUE_AQ_URL), // 获取用户角色列表
  outsourcedTreeDataIpsm: postParamsTY('controlGroupInfo/getHospitalOfficeList', __PATH.VUE_AQ_URL), // 新基础 左侧列表
  getStaffListByOfficeOrTeamIpsm: postParamsTY('controlGroupInfo/getStaffListByOfficeOrTeam', __PATH.VUE_AQ_URL), // 新基础 右侧列表
  // ----------------------------环境监测--------------------------------
  GetDyeingConfig: postRequest('environmentConfig/selectEnvironmentConfig', __PATH.VUE_IEMC_API), // 获取染色区间配置
  UpdateDyeingConfig: postRequest('environmentConfig/updateEnvironmentConfig', __PATH.VUE_IEMC_API), // 修改染色区间配置
  GetEnvirOverview: postRequest('environment/getEnvironmentStatistics', __PATH.VUE_IEMC_API), // 获取环境总览
  GetMonitoringList: postRequest('environment/selectEnvironmentInfoByParamId', __PATH.VUE_IEMC_API), // 获取环境监测详情列表
  GetMonitoringItem: postRequest('environment/selectTodayMaxOrMinInfoByParamId', __PATH.VUE_IEMC_API), // 获取环境监测最大最小值信息
  GetMonitoringParamDetails: postRequest('environment/selectParameterInfoBySurveyCode', __PATH.VUE_IEMC_API), // 获取环境监测详情
  UpdateDateTimeByParamId: postRequest('environment/updateDateTimeByParamId', __PATH.VUE_IEMC_API), // 根据月分获取监测数据
  // ---------------------------------------------给排水---------------------------------------------
  getWaterOverview: postRequest('waterSupply/waterOverview', __PATH.VUE_IEMC_API), // h获取监测模块 总览给排水数据
  // ---------------------------------------------安防---------------------------------------------
  getSensorList: postRequest('sensor/selectSensorList', __PATH.VUE_IEMC_API), // 获取传感器列表
  getOtherSensorList: postRequest('sensor/selectOthersSysSensorList', __PATH.VUE_IEMC_API), // 获取其他传感器列表
  surveyNoIsExist: getRequest('surveyAndParameter/surveyNoIsExist', __PATH.VUE_IEMC_API), // 查询监测项编号是否存在
  batchInsertSurveyAndParameter: postRequest('surveyAndParameter/batchInsertSurveyAndParameter', __PATH.VUE_IEMC_API), // 批量新增监测项
  getStatisticsOverview: getRequest('security/statisticsOverviewAll', __PATH.VUE_IEMC_API), // 安防运行总览统计数量
  getSecurityOverview: postRequest('security/getSecurityOverview', __PATH.VUE_IEMC_API), // 安防运行总览统计数量
  getVideoUrlByDeviceCode: getRequest('cameraManage/getVideoUrlByDeviceCode', __PATH.VUE_IEMC_API), // 摄像头视频地址
  // ----------------------------------------------电梯---------------------------------------------
  getElevatorPullDownList: post_iemc('elevator/getPullDownList'), // 获取电梯下拉列表
  getElevatorStatistics: post_iemc('elevator/getStatistics'), // 大屏监控左上角统计数据
  getIaasStatistics: postParamsQS('asset/assetDetails/getElevatorBrand', __PATH.VUE_ICIS_API), // 大屏监控资产统计图
  // getReasonStatisticPie: post_iemc('policeHistory/getReasonStatisticPie'), // 获取按类型统计饼图
  getElevatorMonitoringList: post_iemc('elevator/getBoardHistogram'), // 大屏监控右侧统计图集合
  getElevatorFloorList: post_iemc('elevator/getFloorHistogrgetam'), // 大屏监控右侧统计图集合(楼层)
  getElevatorFaultData: post_iemc('policeHistory/surveyPoliceGroup'), // 根据监测实体获取当前监测实体下不同参数报警统计数据
  getFloorAlarmData: post_iemc('policeHistory/surveyFloorPoliceGroup'), // 获取当前实体下的报警楼层分布
  getStaticPoliceList: post_iemc('policeHistory/getStaticPoliceList'), // 未处理+当日报警分页查询
  getElevatorParticulars: post_iemc('elevator/selectElevatorParticulars'), // 实时监测 监测实体详情
  getAssetDetailsByAssetIds: postParamsQS('asset/assetDetails/getAssetDetailsByAssetIds', __PATH.VUE_ICIS_API), // 根据电梯关联设备资产id获取资产详情
  getSurveyParameterListOld: post_iemc('surveyAndParameter/getSurveyParameterList'), // 分页查询监测项列表
  getDataServerListOld: post_iemc('dataServer/getDataServerList'), // 主机列表查询
  GetEntityMenuListOld: post_iemc('entityMenu/getEntityMenuList'), // 获取菜单
  setEntityMenuOld: post_iemc('entityMenu/setEntityMenu'), // 新增菜单
  updateEntityMenuOld: post_iemc('entityMenu/updateEntityMenu'), // 修改菜单
  delEntityMenuOld: post_iemc('entityMenu/delEntityMenu'), // 删除菜单
  getSurveyParameterOneOld: post_iemc('surveyAndParameter/getSurveyParameterOne'), // 监测项详情
  insertSurveyAndParameterOld: post_iemc('surveyAndParameter/insertSurveyAndParameter'), // 监测项新增
  updateSurveyAndParameterOld: post_iemc('surveyAndParameter/updateSurveyAndParameter'), // 监测项编辑
  delSurveyAndParameterOld: post_iemc('surveyAndParameter/delSurveyAndParameter'), // 监测项删除
  getOpenNumByDays: post_iemc('elevator/getOpenNumByDays'), // 实时监测 监测实体详情 30天开关门次数
  getDeviationByDays: post_iemc('elevator/getDeviationByDays'), // 实时监测 监测实体详情 偏移趋势
  getVideoList: post_iemc('elevator/getVideoList'), // 实时监测 监测实体详情 摄像机列表
  getHlvAddress: post_iemc('elevator/getHlvAddress'), // 实时监测 根据id获取对应视屏地址
  getCameraListByProjectCode: post_iemc('cameraManage/getCameraListByProjectCode'), // 电梯绑定摄像机列表
  getDateDetails: post_iemc('realMonitoring/getDateDetails'), // 电梯绑定摄像机列表
  alarmTypeList: getRequest('policeHistory/clinePoliceType', __PATH.VUE_IEMC_ELEVATOR_API), // 报警类型列表
  GetPoliceStateCount: postRequest('policeHistory/getPoliceStateCount', __PATH.VUE_IEMC_ELEVATOR_API), // 获取设备报警统计
  openDoorList: postRequest('client/getOpenDoorList', __PATH.VUE_IEMC_ELEVATOR_API), // 开门次数统计
  // ----------------------------应用--------------------------------
  uploadIcon: postFile('SysMenu/upload', __PATH.VUE_SYS_API, 'formdata'), // 上传文件
  GetAllMenuList: getRequest('SysMenu/appCenterMenuList', __PATH.VUE_SYS_API), // 获取所有菜单不包含按钮
  AddOrUpdateApp: postRequest('appCenter/saveOrUpdateApplication', __PATH.VUE_SYS_API), // 新增、修改应用
  GetApplicationList: postRequest('appCenter/getApplicationList', __PATH.VUE_SYS_API), // 获取应用管理中心列表
  GetAppInfo: getRequest('appCenter/echoApplicationInfo', __PATH.VUE_SYS_API), // 获取应用详情
  RemoveApp: postRequest('appCenter/removeApplication', __PATH.VUE_SYS_API), // 删除应用
  GetMyApplyList: getRequest('appCenter/getMyApplicationList', __PATH.VUE_SYS_API), // 获取我的应用列表
  ConfigMyApply: postRequest('appCenter/configMyApplication', __PATH.VUE_SYS_API), // 配置我的应用
  // ----------------------------医废--------------------------------
  getHomeOfficeWasteState: postParamsQS('ihcrsInterfaceController/getHomeOfficeWasteState', __PATH.VUE_APP_IMWS_API), // 医疗废物情况
  getCountGroupByColumnName: postParamsQS('ihcrsInterfaceController/getCountGroupByColumnName', __PATH.VUE_APP_IMWS_API), // 今日医疗废物数量
  getTodayStationInfo: postParamsQS('ihcrsInterfaceController/getTodayStationInfo', __PATH.VUE_APP_IMWS_API), // 出入站概况
  getSumByOfficeId: postParamsQS('ihcrsInterfaceController/getSumByOfficeId', __PATH.VUE_APP_IMWS_API), // TOP10科室数据统计
  getHomeMonthChart: postParamsQS('ihcrsInterfaceController/getHomeMonthChart', __PATH.VUE_APP_IMWS_API), // 医疗废物重量趋势
  getHomeYearChart: postParamsQS('ihcrsInterfaceController/getHomeYearChart', __PATH.VUE_APP_IMWS_API), // 医疗废物重量趋势
  getCollectionRecord: postParamsQS('ihcrsStatisticsInterfaceController/wasteweighrecord/wasteWeighRecord/data', __PATH.VUE_APP_IMWS_API), // 收集记录列表
  getCollectionRecordSum: postParamsQS('ihcrsStatisticsInterfaceController/wasteweighrecord/wasteWeighRecord/gatherDataSum', __PATH.VUE_APP_IMWS_API), // 收集记录列表统计
  getInboundRecordList: postParamsQS('ihcrsStatisticsInterfaceController/rfidrecord/rfidRecord/newData', __PATH.VUE_APP_IMWS_API), // 入站记录列表
  inboundDataSum: postParamsQS('ihcrsStatisticsInterfaceController/rfidrecord/rfidRecord/inboundDataSum', __PATH.VUE_APP_IMWS_API), // 入站数据汇总
  getNewClassifyStatistics: postParamsQS('ihcrsStatisticsInterfaceController/newStatisticsController/getNewClassifyStatistics', __PATH.VUE_APP_IMWS_API), // 综合报表
  getOutboundRecordList: postParamsQS('ihcrsStatisticsInterfaceController/deliveryrecord/deliveryRecord/getOutboundList', __PATH.VUE_APP_IMWS_API), // 出站记录列表
  outboundDataSum: postParamsQS('ihcrsStatisticsInterfaceController/deliveryrecord/deliveryRecord/outboundDataSum', __PATH.VUE_APP_IMWS_API), // 出站记录统计项
  calendarData: postParamsQS('ihcrsStatisticsInterfaceController/deliveryrecord/deliveryRecord/calendarData', __PATH.VUE_APP_IMWS_API), // 出站记录日历模式数据
  outTableList: postParamsQS('ihcrsStatisticsInterfaceController/wasteweighrecord/wasteWeighRecord/listData', __PATH.VUE_APP_IMWS_API), // 出站记录列表日历模式
  timeoutTableList: postParamsQS('ihcrsStatisticsInterfaceController/rfidrecord/rfidRecord/expiredData', __PATH.VUE_APP_IMWS_API), // 超时记录列表日历模式
  outImageUpload: postParamsQS('ihcrsInterfaceController/saveDeliveryRecord', __PATH.VUE_APP_IMWS_API), // 保存运出图片
  selectStaffList: postParamsQS('ihcrsInterfaceController/selectStaffList', __PATH.VUE_APP_IMWS_API), // 获取人员列表
  manualBatchSave: postRequest('ihcrsStatisticsInterfaceController/batchSaveController/manualBatchSave', __PATH.VUE_APP_IMWS_API), // 手动录入
  uploadTemplate: postFile('ihcrsStatisticsInterfaceController/newExpiredData', __PATH.VUE_APP_IMWS_API, 'formdata'), // 上传模板
  getDailyBoxCount: postParamsQS('ihcrsInterfaceController/getDailyBoxCount', __PATH.VUE_APP_IMWS_API), // 获取每日运出的箱数
  // ----------------------------App应用管理--------------------------------
  GetMessageList: postRequest('message/pageMessage', __PATH.VUE_NEWS_API), // 消息管理列表
  DeleteMessage: deleteFn('message/deleteMessage', __PATH.VUE_NEWS_API), // 删除消息
  RevocationMessage: getRequest('message/revocationMessage', __PATH.VUE_NEWS_API), // 消息撤回
  PublishMessage: getRequest('message/publishMessage', __PATH.VUE_NEWS_API), // 发布消息
  InsertMessage: postRequest('message/insertMessage', __PATH.VUE_NEWS_API), // 新增消息
  GetMessageInfo: getRequest('message/echoMessage', __PATH.VUE_NEWS_API), // 获取消息详情
  UpdateMessage: postRequest('message/updateMessage', __PATH.VUE_NEWS_API), // 修改消息
  GetMsgType: getRequest('message/selectMsgType', __PATH.VUE_NEWS_API), // 获取消息类型
  SysAppMenuList: postQuarters('SysMenu/listApp', __PATH.VUE_SYS_API), // 查询App菜单
  SysAppMenu: postQuarters('SysMenu/indexApp', __PATH.VUE_SYS_API), // 获取App菜单树
  GetAppMenuParent: getRequest('SysMenu/addAppMenuParent', __PATH.VUE_SYS_API), // 获取App所属模块树
  indexSelectApp: postQuarters('SysMenu/indexSelectApp', __PATH.VUE_SYS_API), // 查询App菜单下级
  GetAppCategoryList: getRequest('SysMenu/appCategoryList', __PATH.VUE_SYS_API), // 获取应用分类
  // ----------------------------给排水--------------------------------
  GetRealWaterMonitoringList: postRequest('waterSupply/selectRealWaterMonitoringList', __PATH.VUE_IEMC_API), // 获取监测项列表
  WaterOverviewByTime: postRequest('waterSupply/waterOverviewByTime', __PATH.VUE_IEMC_API), // 运行总览 高液位 低液位 故障数量 离线数量统计
  WaterSupplyTotal: postRequest('waterSupply/waterSupplyTotal', __PATH.VUE_IEMC_API), // 排污总量和给水总量
  WaterPumpTotal: postRequest('waterSupply/waterPumpTotal', __PATH.VUE_IEMC_API), // 获取水泵运行统计
  WaterRunTime: postRequest('waterSupply/waterRunTime', __PATH.VUE_IEMC_API), // 获取水泵运行时长
  GetWaterViewDetail: postRequest('waterSupply/waterViewDetail', __PATH.VUE_IEMC_API), // 获取统计详情
  GetParameterListByImsCode: postRequest('waterSupply/getParameterListByImsCode', __PATH.VUE_IEMC_API), // 获取检测项详情
  YearTimeByParamId: postRequest('waterSupply/yearTimeByParamId', __PATH.VUE_IEMC_API), // 获取单个检测项分月数据
  // ----------------------------监测/报警统计--------------------------------
  CountMonitoringNum: postRequest('security/countMonitoringNum', __PATH.VUE_IEMC_API), // 统计监测总览设备数量
  countMonitoringNumByMenuCode: postRequest('security/countMonitoringNumByMenuCode', __PATH.VUE_IEMC_API), // 统计监测总览设备数量
  GetPoliceTrend: postRequest('security/getPoliceTrend', __PATH.VUE_IEMC_API), // 安防总览报警趋势
  GetPoliceInfo: postRequest('security/getPoliceInfo', __PATH.VUE_IEMC_API), // 安防总览报警数据
  OnOrOfflineRecord: postRequest('security/onOrOfflineRecord', __PATH.VUE_IEMC_API), // 在离线列表详情
  CountOffLineDistribute: postRequest('security/countOffLineDistribute', __PATH.VUE_IEMC_API), // 统计监测项离线分布
  CountMonitoringOffLineData: postRequest('security/countMonitoringOffLineData', __PATH.VUE_IEMC_API), // 统计离线排行
  // ----------------------------照明--------------------------------    // 保存今日运行模式
  insertLightingDict: postFormData('lightingDict/insertLightingDict', __PATH.VUE_IEMC_API), // 分组新增
  deleteLightingDict: postFormData('lightingDict/deleteLightingDict', __PATH.VUE_IEMC_API), // 分组删除
  updateLightingDict: postFormData('lightingDict/updateLightingDict', __PATH.VUE_IEMC_API), // 分组编辑
  getActuatorList: getRequest('surveyAndParameter/getActuatorList', __PATH.VUE_IEMC_API), // 获取控制器字典
  // updateLightingGroup: postFormData('surveyAndParameter/updateLightingGroup', __PATH.VUE_IEMC_API), // 分组划分
  groupOperationMonitoring: post_iemc('lightingOperationMonitoring/groupOperationMonitoring'), // 总览分组查询
  lightOpenOrClose: post_iemc('lightingOperationMonitoring/openOrClose'), // 照明控制
  countOverview: post_iemc('lightingOperationMonitoring/countOverview'), // 照明头部总览
  addOperationRecord: post_iemc('lightingOperationMonitoring/addOperationRecord'), // 照明自动切换其他状态提交
  saveWeatherPattern: post_iemc_notInfo('controlStrategy/saveWeatherPattern', __PATH.VUE_IEMC_API), // 控制策略管理 新增场景
  selectWeatherPatternAllList: post_iemc_notInfo('controlStrategy/selectWeatherPatternAllList', __PATH.VUE_IEMC_API), // 控制策略管理 模式列表
  delWeatherPatternById: post_iemc_notInfo('controlStrategy/delWeatherPatternById', __PATH.VUE_IEMC_API), // 控制策略管理 模式删除
  selectTimeTableByPatternId: post_iemc_notInfo('controlStrategy/selectTimeTableByPatternId', __PATH.VUE_IEMC_API), // 控制策略管理 模式下场景列表
  saveTimeTable: post_iemc_notInfo('controlStrategy/saveTimeTable', __PATH.VUE_IEMC_API), // 控制策略管理 模式下场景保存
  delTimeTableById: post_iemc_notInfo('controlStrategy/delTimeTableById', __PATH.VUE_IEMC_API), // 控制策略管理 模式下场景删除
  getLightingGroupByPattern: post_iemc_notInfo('lightingDict/getLightingGroupByPattern', __PATH.VUE_IEMC_API), // 控制策略管理 列表渲染
  associatedScene: post_iemc_notInfo('lightingDict/associatedScene', __PATH.VUE_IEMC_API), // 控制策略管理 场景修改
  GetOperationRecordList: post_iemc('controlStrategy/getOperationRecordList'), // 操作记录查询
  GetSelectAllCalendar: post_iemc('calendar/selectAllCalendar'), // 根据年/月获取运行日历管理
  GetSelectHolidayByYear: post_iemc('calendar/selectHolidayByYear'), // 根据年份获取节假日
  SaveCalendar: post_iemc('calendar/saveCalendar'), // 保存运行日历计划
  getToDayScenes: post_iemc('runToDay/getToDayScenes'), // 获取今日场景
  getCountRunToDayState: post_iemc('runToDay/getCountRunToDayState'), // 获取今日总览
  getToDayLightingGroup: post_iemc('runToDay/getToDayLightingGroup'), // 获取今日分组对应场景
  setToDayDataByRedis: post_iemc('runToDay/setToDayDataByRedis'), // 获取今日场景 策略管理页面
  saveTimeTableToRedis: post_iemc_notInfo('runToDay/saveTimeTableToRedis', 'query', __PATH.VUE_IEMC_API), // 新增场景配置时间表
  delTimetableToRedis: post_iemc_notInfo('runToDay/delTimetableToRedis', 'query', __PATH.VUE_IEMC_API), // 删除场景配置时间表
  updateConnectionRelation: post_iemc_notInfo('runToDay/updateConnectionRelation', 'query', __PATH.VUE_IEMC_API), // 今日运行模式 场景修改
  saveOrCancel: post_iemc_notInfo('runToDay/saveOrCancel', 'query', __PATH.VUE_IEMC_API), // 保存今日运行模式
  selectPreviewData: post_iemc('runToDay/selectPreviewData'), // 预览今日运行模式
  unicomStatus: post_iemc('runToDay/unicomStatus'), // 获取联通状态
  getControlDiff: post_iemc('lightingOperationMonitoring/getControlDiff'), // 获取控返不一致列表
  getTodayModeList: post_iemc('controlStrategy/getOperationRecordCountOpenNum'), // 获取控返不一致列表
  GetLightingStatistics: post_iemc('lightingStatistics/selectLighting'), // 获取照明统计
  GetSpaceLightingCircuit: post_iemc('lightingStatistics/selectLightingByFloorIdOrSpaceId'), // 获取空间照明回路
  modeSwitching: post_iemc('lightingOperationMonitoring/modeSwitching'), // 手自动切换
  AddLightingControl: post_iemc('lightingControl/saveLightingControl'), // 新增照明联控配置
  SelectInfoByType: post_iemc('surveyAndParameter/selectInfoByType'), // 获取监测实体类型下的监测实体信息
  GetLightingControlList: getRequest('lightingControl/getLightingControlList', __PATH.VUE_IEMC_API), // 联控配置列表
  DeleteLightingControl: getRequest('lightingControl/deleteLightingControl', __PATH.VUE_IEMC_API), // 删除联控配置
  GetOneLightingControl: getRequest('lightingControl/selectOneLightingControl', __PATH.VUE_IEMC_API), // 查询联控配置
  UpdateLightingControl: post_iemc('lightingControl/updateLightingControl'), // 修改照明联控配置
  // ------------------------------------------------冷热源--------------------------------------------
  getColdHostStatistics: postRequest('coleHot/statisticsOverview', __PATH.VUE_IEMC_API), // 冷热源运行总览
  getSurveyControlParameterOne: postRequest('surveyAndParameter/getSurveyControlParameterOne', __PATH.VUE_IEMC_API), // 监测项详情
  insertControlSurveyAndParameter: postRequest('surveyAndParameter/insertControlSurveyAndParameter', __PATH.VUE_IEMC_API), // 监测项新增
  updateControlSurveyAndParameter: postRequest('surveyAndParameter/updateControlSurveyAndParameter', __PATH.VUE_IEMC_API), // 监测项编辑
  // SCADA接口配置
  uploadImage: postFormData('fileOperation/upload/image'), //  图片上传
  uploadImageBase: postFormData('fileOperation/upload/imageBase'), //  图片上传
  folderAdd: postRequest('scalaDrawing/folderAdd'), //  文件夹添加
  folderDel: postRequest('scalaDrawing/folderDel'), //  文件夹删除
  scalaImageDel: postFormData('scalaImage/scalaImageDel'), //  组件图纸删除
  folderUpdate: postRequest('scalaDrawing/folderUpdate'), //  文件夹修改
  getFolderByRole: postRequest('scalaDrawing/getFolderByRole'), //  根据身份查询文件夹
  scalaImageAdd: postRequest('scalaImage/scalaImageAdd'), //  组件添加
  scalaImageUpdate: postRequest('scalaImage/scalaImageUpdate'), //  组件或图纸修改
  getScalaImageById: postFormData('scalaImage/getScalaImageById'), //  查询组件详细信息
  getScaleImgByProjectCode: postRequest('realMonitoring/getScaleImgByProjectCode', __PATH.VUE_IEMC_API), //  获取图纸列表通过projectcode
  // 报警配置接口
  getAlarmConfigTree: getRequest('alarmConfig/getTree', __PATH.VUE_IEMC_API), // 获取报警配置树形结构
  insertAlarmConfig: postRequest('alarmConfig/insertAlarmConfig', __PATH.VUE_IEMC_API), // 报警配置新增
  getAlarmConfigByPage: getRequest('alarmConfig/getAlarmConfigByPage', __PATH.VUE_IEMC_API), // 报警配置查询
  policeDeleteById: postFormData('alarmConfig/deleteById', __PATH.VUE_IEMC_API), // 报警配置删除
  getAlarmConfigById: getRequest('alarmConfig/getAlarmConfigById', __PATH.VUE_IEMC_API), //
  updateAlarmConfig: postRequest('alarmConfig/updateAlarmConfig', __PATH.VUE_IEMC_API), // 更新报警配置
  getDictionaryValueById: postRequest('dictionary/getDictionaryValueById', __PATH.DICTIONAR_URL), // 报警参数传参
  getOtherSysValueById: postRequest('sensor/getOtherSysValueById', __PATH.VUE_IEMC_API), // 报警参数传参
  getDictionaryValueByIds: postRequest('dictionary/getDictionaryValueByIds', __PATH.DICTIONAR_URL), // 获取报警参数下拉框
  getAlarm: getRequest('alarmDict/getAlarm', __PATH.VUE_IEMC_API), // 获取报警类型
  getAlarmSystem: getRequest('alarmConfig/getAlarmSystem', __PATH.VUE_IEMC_API), // 获取报警系统
  getAlarmEntityList: getRequest('alarmConfig/getAlarmEntityList', __PATH.VUE_IEMC_API), // 获取报警实体对象
  getAlarmEntityObjectList: postRequest('surveyAndParameter/queryAlarmObject', __PATH.VUE_IEMC_API), // 获取报警实体对象（新）
  getEntityTypeList: postRequest('dictionary/getEntityTypeList', __PATH.VUE_IEMC_API), // 报警级别
  // ----------------------------医用气体--------------------------------
  getPoliceCount: post_iemc('policeHistory/getPoliceCount'), // 获取报警数量
  getTrendStatisticLine: post_iemc('policeHistory/getTrendStatisticLine'), // 今日本月本年报警数量统计--近30日报警统计图
  getReasonStatisticPie: post_iemc('policeHistory/getReasonStatisticPie'), // 获取按类型统计饼图
  getPoliceList: post_iemc('policeHistory/getPoliceList'), // 报警中心列表分页查询
  DeletePoliceHistory: post_iemc('policeHistory/deletePoliceHistory'), // 删除报警记录
  getPoliceView: post_iemc('policeHistory/getPoliceView'), // 获取报警详情
  disposePoliceBatch: post_iemc('policeHistory/disposePoliceBatch'), // 报警批处理
  disposePolice: postFormData('policeHistory/disposePolice', __PATH.VUE_IEMC_API), // 报警处理
  exportExcel: iemc_downFile('policeHistory/exportExcel'), // 导出报警列表
  getMonitorStatistic: post_iemc('policeHistory/getMonitorStatistic'), // 实时监测处报警数量与列表统计
  getMonitorStatisticData: post_iemc('alarm/getAlarmCountByDiffTime'), // 实时监测处报警数量与列表统计-新
  getEntityMenuList: post_iemc('entityMenu/getEntityMenuList'), // 根据项目查询监测实体菜单
  getHistoryList: post_iemc('realMonitoring/getHistoryList'), // 查询历史数据
  // getScadaList: post_iemc('entityMenu/getScalaEntityMenuList'), // 实时监测--scala
  getFillingList: post_iemc('fillingRecord/getFillingList'), // 充装记录
  ExportFillingRecord: iemc_downFile('fillingRecord/exportFillingRecord'), // 充装记录导出
  UpdateFilling: post_iemc('fillingRecord/updateFilling'), // 充装记录
  DeleteFilling: post_iemc('fillingRecord/deleteFilling'), // 删除充装记录
  getStaffList: post_iemc('staffManage/getStaffList'), // 值班人员
  GetFillingPersonnelAll: post_iemc('fillingPersonnel/getFillingPersonnelAll'), // 查询所有充装人员
  getAliasDictList: post_iemc('dictionary/selectDictAliasAll'), // 查询项目别名字典列表
  // ----------------------------统计分析--------------------------------
  getParameterCensus: post_iemc('equipmentAmbient/getParameterCensus'), // 环境监测列表
  getStatisticAsMonitor: post_iemc('policeStatistic/getStatisticAsMonitor'), // 电梯-设备报警统计
  getDeviceOperationStatistic: post_iemc('policeStatistic/getDeviceOperationStatistic'), // 电梯-设备运行统计
  getStatisticAsPoliceCount: post_iemc('policeStatistic/getStatisticAsPoliceCount'), // 电梯-报警类型统计
  getElevatorStatisticAsMonitor: post_iemc('elevator/getHistogramByType'), // 电梯-运行参数统计
  getElevatorFloorStatisticAsMonitor: post_iemc('elevator/getFloorHistogrgetam'), // 电梯-运行楼层统计
  GetSouthTowerBOneOxygen: post_iemc('gasesStatistic/getSouthTowerBOneOxygen'), // 总用氧分析列表
  GetSouthTowerBOneHistogram: post_iemc('gasesStatistic/getSouthTowerBOneHistogram'), // 总用氧分析柱状图
  getStaAsPolice: post_iemc('gasesStatistic/getStaAsPolice'), // 医用气体-报警统计分析
  getOxygenUseStat: post_iemc('gasesStatistic/getOxygenUseStat'), // 医用气体-用氧统计分析
  getOxygenUseColumnStat: post_iemc('gasesStatistic/getOxygenUseColumnStat'), // 医用气体-获取用氧量柱状图
  getOxygenUseTrendStat: post_iemc('gasesStatistic/getOxygenUseTrendStat'), // 医用气体-用氧走势分析
  getAllSurveyByProjectCode: post_iemc('surveyAndParameter/getAllSurveyByProjectCode'), // 监测项下拉列表
  exportParameterCensus: iemc_downFile('equipmentAmbient/exportParameterCensus'), // 设备环境统计相关导出
  policeStatisticExportWord: iemc_downFile('policeStatistic/exportWord'), // 电梯-统计导出word
  gasesStatisticExportWord: iemc_downFile('gasesStatistic/exportWord'), // 医用气体-统计导出word
  getOxygenUseStatToExcel: downFile('gasesStatistic/getOxygenUseStatToExcel', __PATH.VUE_IEMC_API), // 医用气体-统计导出Excel
  // ----------------------------冷热源--------------------------------
  GetColdOverview: postRequest('coleHot/selectOverview', __PATH.VUE_IEMC_API), // 获取冷热源统计
  GetRunningTrend: postRequest('coleHot/selectRunningTrend', __PATH.VUE_IEMC_API), // 获取运行趋势
  GetEntityTypeGroup: postRequest('surveyAndParameter/selectEntityTypeGroup', __PATH.VUE_IEMC_API), // 获取实体类型分组
  GetColdHotTrend: postRequest('coleHot/selectColdHotTrend', __PATH.VUE_IEMC_API), // 冷/热趋势
  GetStatisticsInfo: postRequest('coleHot/selectStatisticsInfo', __PATH.VUE_IEMC_API), // 获取实体类型下的运行状态以及故障|离线状态
  // ----------------------------能耗--------------------------------
  integrationLogin: postRequest('sys/login', __PATH.VUE_ENERGY_API), // 登录
  getOauthToken: postQueryQS('oauth/token', __PATH.VUE_ENERGY_API, 'form'), // 获取单点登录token
  getModelEnergyDataList: getRequest('gy-service-energy-core/entEnergyData/getModelEnergyDataList', __PATH.VUE_ENERGY_API), // 能耗用电实时监测检测日、周、月、年的统计
  getEnergyCategoryTree: getRequest('gy-service-energy-core/entSysEnergyCategoryInfo/getEnergyCategoryTree', __PATH.VUE_ENERGY_API), // 能耗用电实时监测检测日、周、月、年的统计
  getIndicatorQuery: postRequest('gy-service-energy-core/quotaUsage/queryFixQuantity', __PATH.VUE_ENERGY_API), // 定額信息
  getRatedData: postRequest('energyConsumption/getRatedData', __PATH.VUE_ICIS_LZP), // 额定用电
  getAlarmInfoList: postRequest('energyConsumption/getAlarmInfoList', __PATH.VUE_ICIS_LZP), // 超限&&列表
  getTargetEnergyList: postRequest('inspectData/queryInspectData', __PATH.SPACE_API), // 指标能耗列表
  delTargetEnergy: postRequest('inspectData/deleteInspectData', __PATH.SPACE_API), // 指标能耗删除 && 批量删除
  setTargetEnergy: postRequest('inspectData/insertInspectData', __PATH.SPACE_API), // 添加指标能耗
  editTargetEnergy: postRequest('inspectData/getInspectDataById', __PATH.SPACE_API), // 通过id查询
  updateTargetEnergy: postRequest('inspectData/updateInspectData', __PATH.SPACE_API), // 编辑更新
  // ---------------------------- 配电区间配置相关接口--------------------------------
  selectSection: postRequest('electricitySection/selectSection', __PATH.VUE_IEMC_API), // 查询配电区间配置---根据菜单查询
  saveSection: postRequest('electricitySection/saveSection', __PATH.VUE_IEMC_API), // 保存配电区间配置
  selectSectionOne: getRequest('electricitySection/selectSectionOne', __PATH.VUE_IEMC_API), // 修改回显
  updateSection: postRequest('electricitySection/updateSection', __PATH.VUE_IEMC_API), // 修改配电区间配置
  deleteSection: getRequest('electricitySection/deleteSection', __PATH.VUE_IEMC_API), // 删除配电区间中的某一配置
  deleteSectionEntity: getRequest('electricitySection/deleteSectionEntity', __PATH.VUE_IEMC_API), // 删除配电区间配置
  selectSectionTree: getRequest('electricitySection/selectSectionTree', __PATH.VUE_IEMC_API), // 获取区间配置下拉树
  // ---------------------------- 配电总览--------------------------------
  GetParentMenuList: postRequest('entityMenu/getParentMenuList', __PATH.VUE_IEMC_API), // 获取一级菜单
  GetEquipmentOperationMonitor: postRequest('surveyAndParameter/selectEntityType', __PATH.VUE_IEMC_API), // 设备运行总览
  GetEnvironmentMonitor: postRequest('surveyAndParameter/selectEnvironSurvey', __PATH.VUE_IEMC_API), // 环境监测信息
  selectTime: getRequest('electricityTime/selectTime', __PATH.VUE_IEMC_API), // 查询配电时刻
  saveTime: postRequest('electricityTime/saveTime', __PATH.VUE_IEMC_API), // 保存配电时段配置
  selectEntityGroup: postRequest('surveyAndParameter/selectEntityGroup', __PATH.VUE_IEMC_API), // 获取菜单分组下的监测实体类型
  GetElectricityKeyDevice: postRequest('surveyAndParameter/selectElectricityKeyDevice', __PATH.VUE_IEMC_API), // 获取重点设备
  selectDataReport: postRequest('electricity/selectDataReport', __PATH.VUE_IEMC_API), // 电力数据报表查询
  selectExtremumReport: postRequest('electricity/selectExtremumReport', __PATH.VUE_IEMC_API), // 电力极值报表查询
  selectEnergyReport: postRequest('electricity/selectEnergyReport', __PATH.VUE_IEMC_API), // 电力用能报表查询
  selectTimeFrame: postRequest('electricity/selectTimeFrame', __PATH.VUE_IEMC_API), // 电力分时段用电
  selectExtremumTree: getRequest('electricity/selectExtremumTree', __PATH.VUE_IEMC_API), // 电力极值报表菜单树
  queryParamList: getRequest('electricPower/queryParamList', __PATH.VUE_IEMC_API), // 电力数据-谐波监测-查询条件
  queryElectMonitoCondition: getRequest('electricPower/queryElectMonitoCondition', __PATH.VUE_IEMC_API), // 电力数据-原始电力数据-查询条件
  queryParamListBySurveyCode: getRequest('electricPower/queryParamListBySurveyCode', __PATH.VUE_IEMC_API), // 电力数据-原始电力数据-查询条件
  queryRawDataList: postRequest('electricPower/queryRawDataList', __PATH.VUE_IEMC_API), // 电力数据-原始电力数据
  queryMenuList: postRequest('electricPower/queryMenuList', __PATH.VUE_IEMC_API), // 电力数据-极值电力数据
  queryAvgPowerData: postRequest('electricPower/queryAvgPowerData', __PATH.VUE_IEMC_API), // 电力数据-平均功率因数
  queryHarmonicMonitorData: postRequest('electricPower/queryHarmonicMonitorData', __PATH.VUE_IEMC_API), // 电力数据-谐波监测
  QueryPowerAnalyseByTotal: postRequest('electricPower/queryPowerAnalyseByTotal', __PATH.VUE_IEMC_API), // 用电分析-总用电量
  QueryPowerAnalyseByTime: postRequest('electricPower/queryPowerAnalyseByTime', __PATH.VUE_IEMC_API), // 用电分析-分时段用电
  GetCameraListByProjectCode: postRequest('cameraManage/getCameraListByProjectCode', __PATH.VUE_IEMC_API), // 获取视频监控设备
  GetSpaceAssetInfo: postRequest('surveyAndParameter/getSpaceAssetInfo', __PATH.VUE_IEMC_API), // 获取一级菜单下监测实体绑定的资产信息
  SelectEntityDateList: postRequest('electricity/selectEntityDateList', __PATH.VUE_IEMC_API), // 查询检测项详情-列表
  SpatialEquipmentStatistics: postParamsQS('planTaskNew/spatialEquipmentStatistics', __PATH.VUE_ICIS_API), // 设备巡检任务统计
  GetElectricFocusList: postRequest('realMonitoring/getElectricFocusList', __PATH.VUE_IEMC_API), // 获取重要设备列表
  GetAlarmAnalysis: postRequest('alarm/record/airPoliceInfoByProjectCode', __PATH.VUE_WARN_API), // 获取空调报警详情
  // ---------------------------- 空间管理--------------------------------
  getTenantList: postRequest('lease/getTenantList', __PATH.SPACE_API), // 查询租户信息
  spaceaUpload: postFile('lease/upload', __PATH.SPACE_API, 'formdata'), // 上传文件
  insertTenant: postRequest('lease/insertTenant', __PATH.SPACE_API), // 保存租户信息
  getTenantById: getRequest('lease/getTenantById', __PATH.SPACE_API), // 查询租户信息
  updateTenant: postRequest('lease/updateTenant', __PATH.SPACE_API), // 修改租户信息
  deleteTenantById: getRequest('lease/deleteTenantById', __PATH.SPACE_API), // 删除租户
  queryTenantAllList: postRequest('lease/queryTenantAllList', __PATH.SPACE_API), // 查询所有的租户信息
  getLeaseListByPage: postRequest('lease/getLeaseListByPage', __PATH.SPACE_API), // 查询租赁信息
  getSpaceDictionaryList: postRequest('spaceApply/getDictionaryList', __PATH.SPACE_API), // 空间申请--获取【用途】字典数据
  addNewObj: postRequest('lease/addNewObj', __PATH.SPACE_API), // 新增租赁管理单
  updateLease: postRequest('lease/updateLease', __PATH.SPACE_API), // 更新空间租赁记录
  queryLeaseById: getRequest('lease/queryLeaseById', __PATH.SPACE_API), // 根据id查询空间租赁记录
  deleteById: getRequest('lease/deleteById', __PATH.SPACE_API), // 删除租赁记录
  queryLeaseHistoryById: getRequest('lease/queryLeaseHistoryById', __PATH.SPACE_API), // 根据空间id查询空间租赁历史记录
  // ----------------------------空间申请--------------------------------
  GetSpaceApplyList: postRequest('spaceApply/querySpaceApplyListByPage', __PATH.SPACE_API), // 查询申请列表
  UpdateSpaceApplyStatus: postRequest('spaceApply/updateApplyStatusByApplyId', __PATH.SPACE_API), // 空间申请单状态更改
  SpaceAllocation: postRequest('spaceApply/spaceAllocation', __PATH.SPACE_API), // 分配空间
  // ----------------------------空间清查--------------------------------
  selectDeptByPage: postRequest('departmentManager/department-manager/selectDeptByPage', __PATH.VUE_SPACE_API), // 分页查询部门空间数量信息列表|lc
  queryDepartListByPage: postRequest('space/queryDepartListByPage', __PATH.SPACE_API), // 分页查询科室信息
  addInventoryTask: postRequest('space/addInventoryTask', __PATH.SPACE_API), // 新增盘点单数据
  queryInventoryTaskListByPage: postRequest('space/queryInventoryTaskListByPage', __PATH.SPACE_API), // 分页查询盘点单
  deleteInventoryTaskById: getRequest('space/deleteInventoryTaskById', __PATH.SPACE_API), // PC-根据盘点单id删除盘点单
  queryInventoryTotal: getRequest('space/queryInventoryTotal', __PATH.SPACE_API), // PC-查询任务总数和进行中的任务
  queryOneInventoryTaskById: getRequest('space/queryOneInventoryTaskById', __PATH.SPACE_API), // PC-根据id查询盘点单
  updateInventoryTaskStatusById: getRequest('space/updateInventoryTaskStatusById', __PATH.SPACE_API), // PC-修改清查单的状态
  updateInventoryById: postRequest('space/updateInventoryById', __PATH.SPACE_API), // PC-根据id更新清查任务
  querySpaceListPageByDepartId: postRequest('space/querySpaceListPageByDepartId', __PATH.SPACE_API), // PC-分页查询空间信息
  querySpaceNum: postRequest('space/querySpaceNum', __PATH.SPACE_API), // PC-查询总空间&未确认&已确认
  exportSpaceInfo: downFile('space/exportSpaceInfo', __PATH.SPACE_API), // PC-导出空间数据
  selectLeafSpaceById: postFormData('departmentManager/department-manager/selectLeafSpaceById', __PATH.VUE_SPACE_API),
  operationLogList: postRequest('operationLog/getOperationLogPage', __PATH.SPACE_API), // 获取操作日志列表
  loginLogList: postRequest('loginLog/getLoginLogPage', __PATH.SPACE_API), // 获取登录日志列表
  addLoginLogList: postRequest('loginLog/saveLoginLog', __PATH.SPACE_API), // 添加登录日志
  sectionMenuList: getRequest('sectionMenu/list', __PATH.VUE_IEMC_API), // 获取区间配置菜单
  sectionMenuSave: postRequest('sectionMenu/save', __PATH.VUE_IEMC_API), // 保存区间配置菜单
  sectionMenuUpdate: postRequest('sectionMenu/update', __PATH.VUE_IEMC_API), // 修改区间配置菜单
  sectionMenuDelete: getRequest('sectionMenu/delete', __PATH.VUE_IEMC_API), // 删除区间配置菜单集合
  // ---------------------------锅炉--------------------------------
  boilerCount: postRequest('boiler/boilerCount', __PATH.VUE_IEMC_API), // 锅炉监测-运行总览-查询锅炉总数
  queryBoilerMonitor: postRequest('boiler/queryBoilerMonitor', __PATH.VUE_IEMC_API), // 锅炉监测-运行总览-锅炉监测
  queryGasMonitoring: postRequest('boiler/queryGasMonitoring', __PATH.VUE_IEMC_API), // 锅炉监测-运行总览-燃气监测
  queryEnvMonitor: postRequest('boiler/queryEnvMonitor', __PATH.VUE_IEMC_API), // 锅炉监测-运行总览-环境监测
  queryPowerAnalyseByPie: postRequest('boiler/queryPowerAnalyseByPie', __PATH.VUE_IEMC_API), // 锅炉监测-运行总览-能耗分析饼图
  queryEnergyAnalyse: postRequest('boiler/queryEnergyAnalyse', __PATH.VUE_IEMC_API), // 锅炉监测-运行总览-能耗分析折线图
  queryEnvMonitorByParamId: postRequest('boiler/queryEnvMonitorByParamId', __PATH.VUE_IEMC_API), // 锅炉监测-运行总览-环境监测详情-参数
  queryEnvMonitorDetail: postRequest('boiler/queryEnvMonitorDetail', __PATH.VUE_IEMC_API), // 锅炉监测-运行总览-环境监测详情
  getBoilerByProjectCode: postRequest('alarm/record/getBoilerByProjectCode', __PATH.VUE_WARN_API), // 锅炉监测-运行总览-环境监测详情
  // 安消防防火分区
  addFireproof: postRequest('fireArea/insertFireAreaData', __PATH.VUE_IEMC_API), // 防火分区新增
  updateFireproof: postRequest('fireArea/updateById', __PATH.VUE_IEMC_API), // 防火分区新增
  fireproofList: postRequest('fireArea/queryFireAreaByPage', __PATH.VUE_IEMC_API), // 防火分区列表
  fireproofDetail: getRequest('fireArea/queryById', __PATH.VUE_IEMC_API), // 防火分区详情
  delFireprool: getRequest('fireArea/deleteById', __PATH.VUE_IEMC_API), // 防火分区详情
  GetDeviceAssetsList: postQs('assetsInfo/listData', __PATH.VUE_APP_IEMS_API, 'form'), // 设备(资产)列表
  getEquimentMaintenance: postQs('deviceRepair/getDeviceRepairProfiles', __PATH.VUE_APP_IEMS_IOMS_API), // 获取指定设备的维修概况
  getEquimentInfo: postQs('assetsInfo/view', __PATH.VUE_APP_IEMS_API), // 获取指定设备的信息
  getEquimentTag: postQs('QRCode/getAssetsLabelList', __PATH.VUE_APP_IEMS_API), // 获取指定设备的标签信息
  assetsExtendsInfoView: postQs('assetsExtendsInfo/view', __PATH.VUE_APP_IEMS_API), // 获取资产转科详情
  getArchivesType: postQs('assetsFileRecord/getAssetsFileType', __PATH.VUE_APP_IEMS_API), // 获取档案分类
  getArchivesList: postQs('assetsFileRecord/listData', __PATH.VUE_APP_IEMS_API), // 获取档案列表
  // 世界谈医院资产
  statisticsAll: postQs('assetsInfo/statistics/all', __PATH.VUE_APP_IEMS_API), // 资产总览
  GetOfficeByUserRole: postQs('baseInfo/getOfficeByStaff', __PATH.VUE_APP_IEMS_API, 'form'),
  GetIemsDictList: postQs('dictUtils/getDictList', __PATH.VUE_APP_IEMS_API, 'form'),
  GetHospitalOfficeLastLevelInfo: postQs('iaasAssetController/getHospitalOfficeLastLevelInfo', __PATH.VUE_APP_IEMS_API, 'form'),
  // 照明字典
  saveDictAlias: postRequest('dictionary/saveDictAlias', __PATH.VUE_IEMC_API), // 新增照明字典
  selectDictAliasList: postRequest('dictionary/selectDictAliasList', __PATH.VUE_IEMC_API), // 照明列表
  dictionaryUploadIcon: postFile('dictionary/uploadIcon', __PATH.VUE_IEMC_API, 'formdata'), // 上传文件
  deleteDictAlias: getRequest('dictionary/deleteDictAlias', __PATH.VUE_IEMC_API), // 删除照明字典别名配置
  selectOneDictAlias: getRequest('dictionary/selectOneDictAlias', __PATH.VUE_IEMC_API), // 查询单一照明字典别名配置
  // 用电
  querySurveyDetail: postRequest('electricitySafe/querySurveyDetail', __PATH.VUE_IEMC_API), // 实体详情查询
  querySurveyCount: postRequest('electricitySafe/querySurveyCount', __PATH.VUE_IEMC_API), // 监控点数等统计
  queryMenuByPage: postRequest('electricitySafe/queryMenuByPage', __PATH.VUE_IEMC_API), // 2、串口查询
  updateDictAlias: postRequest('dictionary/updateDictAlias', __PATH.VUE_IEMC_API), // 2、串口查询
  // ---------------停车场管理
  getExistChargeDetail: getRequest('parking/existChargeDetail', __PATH.VUE_ICIS_LZP), // 出口收费统计
  getOperateTotalDetail: postRequest('parking/operateTotalDetail', __PATH.VUE_ICIS_LZP), // 运营总览
  getParkingSuperviseInfo: getRequest('parking/parkingSuperviseInfo', __PATH.VUE_ICIS_LZP), // 车场监管统计
  getPassagewayStress: getRequest('parking/passagewayStress', __PATH.VUE_ICIS_LZP), // 出入口流量压力统计图表
  getParkingMonit: getRequest('parking/parkingMonit', __PATH.VUE_ICIS_LZP), // 车位监控
  getInParkingRecord: postRequest('parking/inParkingRecord', __PATH.VUE_ICIS_LZP), // 停车场入场记录
  getOutParkingRecord: postRequest('parking/outParkingRecord', __PATH.VUE_ICIS_LZP), // 停车场入场记录
  // 订餐
  getDiningOrder: postRequest('foodOrder/findPage', __PATH.VUE_ICIS_LZP), // 订餐综合查询列表
  getFoodOrderDetails: postRequest('foodOrder/getFoodOrderDetails', __PATH.VUE_ICIS_LZP), // 订餐综合查询详情页
  getComplainList: postRequest('complain/findPage', __PATH.VUE_ICIS_LZP), // 投诉管理列表
  getComplainDetails: postRequest('complain/getComplainDetails', __PATH.VUE_ICIS_LZP), // 投诉管理详情
  // ---------餐饮管理
  getPperationsOverview: getRequest('operate/operationsOverview', __PATH.VUE_ICIS_LZP), // 车位监控
  getDishesSaleTopFive: getRequest('operate/dishesSaleTopFive', __PATH.VUE_ICIS_LZP), // 菜品销售Top5
  getBookingDeptTopFive: getRequest('operate/bookingDeptTopFive', __PATH.VUE_ICIS_LZP), // 订餐科室Top5
  getBookingWayCount: getRequest('operate/bookingWayCount', __PATH.VUE_ICIS_LZP), // 订餐方式占比
  getRestaurantcomplaint: getRequest('operate/restaurantcomplaint', __PATH.VUE_ICIS_LZP), // 餐饮投诉
  getBookingTypeCount: getRequest('operate/bookingTypeCount', __PATH.VUE_ICIS_LZP), // 订餐类别统计
  getDishesSaleCount: postRequest('operate/dishesSaleCount', __PATH.VUE_ICIS_LZP), // 菜品销售统计
  // ----------- 空调末端
  airConditionerOnOrOff: postRequest('airCondition/airConditionerOnOrOff', __PATH.VUE_IEMC_API), // 统计空调按菜单分类开启关闭数量
  getAirConditionerTime: postRequest('airCondition/getAirConditionerTime', __PATH.VUE_IEMC_API), // 统计空调设备运行时长top10
  newAirBreakdown: postRequest('airCondition/newAirBreakdown', __PATH.VUE_IEMC_API), // 统计空调故障top10
  newAirOffLine: postRequest('airCondition/newAirOffLine', __PATH.VUE_IEMC_API), // 统计空调离线top10
  getParamInfoByProjectCode: getRequest('iemcParamState/getParamInfoByProjectCode', __PATH.VUE_IEMC_API), // 统计空调离线top10
  setControl: postRequest('airCondition/control', __PATH.VUE_IEMC_API), // 空调反控
  getOffLineRealMonitorData: postRequest('realMonitoring/getOffLineRealMonitorData', __PATH.VUE_IEMC_API), // 获取指定监测实体下的离线数据
  getHistoryRealMonitorData: postRequest('realMonitoring/getHistoryRealMonitorData', __PATH.VUE_IEMC_API), // 获取指定监测实体下的历史数据(列表)
  getParamSurveyCode: postRequest('iemcParamState/getParamSurveyCode', __PATH.VUE_IEMC_API), // 根据监测实体code获取参数数据---别名版
  getChartData: postRequest('realMonitoring/getChartData', __PATH.VUE_IEMC_API), // 获取指定监测实体下的历史数据(图表)
  // ---------------------值排班
  getDutyRota: post_iemc('duty/getDutyRota'), // 值班表列表
  // getStaffList: post_iemc('staffManage/getStaffList'), // 值班人员
  saveDutyRota: post_iemc('duty/saveDutyRota'), // 保存排班
  getDutyToday: post_iemc('duty/getDutyToday'), // 今日值班信息
  signIn: post_iemc('duty/signIn'), // 人员签到
  getSignListForPage: post_iemc('duty/getSignListForPage'), // 值班记录
  // 设备资产
  getVideoPath: postParamsQS('/asset/assetDetails/getVideoPath', __PATH.VUE_ICIS_API), // 获取视频路径
  getDictionaryIotList: postParamsQS('/asset/assetDetails/getDictionaryList', __PATH.VUE_ICIS_API), // 获取视频路径
  getIotSensorList: postParamsQS('/asset/assetDetails/sensorList', __PATH.VUE_ICIS_API), // 获取视频路径
  // 指标
  // 指标配置
  targetConfigList: postRequest('inspectBase/getCycleRulePage', __PATH.SPACE_API), // 指标配置列表
  updateConfigRule: postRequest('inspectBase/saveCycleRule', __PATH.SPACE_API), // 新增编辑指标配置
  targetConfigDetail: postRequest('inspectBase/getCycleRuleDetail', __PATH.SPACE_API), // 指标配置详情
  delTargetConfig: postRequest('inspectBase/deleteCycleRule', __PATH.SPACE_API), // 指标配置删除
  openRule: postRequest('inspectBase/openCycleRule', __PATH.SPACE_API), // 指标配置开启
  closeRule: postRequest('inspectBase/closeCycleRuleById ', __PATH.SPACE_API), // 指标配置关闭
  haveOpenCycleRule: postRequest('inspectBase/haveOpenCycleRule', __PATH.SPACE_API), // 指标配置删除
  getCycleType: postRequest('inspectBase/getCycleRuleType', __PATH.SPACE_API), // 指标配置删除
  getPresetTime: postRequest('inspectBase/getTimeList', __PATH.SPACE_API), // 指标配置删除
  // 指标模板
  templateType: postRequest('inspectBase/getTemplateType', __PATH.SPACE_API), // 考察类型
  templateManageList: postRequest('inspectBase/getTemplateManagePage', __PATH.SPACE_API), // 指标模板列表
  delTemplateById: postRequest('inspectBase/deleteTemplateManageById', __PATH.SPACE_API), // 指标模板列表
  saveTemplate: postRequest('inspectBase/saveTemplateManage', __PATH.SPACE_API), // 指标模板保存
  targetTmpDetail: postRequest('inspectBase/getTemplateManageById', __PATH.SPACE_API), // 指标模板保存
  // 指标计划
  getTargetPlanStatistics: getRequest('inspect/statistics', __PATH.SPACE_API), // 指标计划统计
  getTargetPlanList: postRequest('inspect/selectPlanList', __PATH.SPACE_API), // 指标计划列表
  saveTargetPlan: postRequest('inspect/insertPlan', __PATH.SPACE_API), // 新增指标计划
  updateTargetPlan: postRequest('inspect/updatePlan', __PATH.SPACE_API), // 新增指标计划
  getTargetPlanById: getRequest('inspect/selectPlanInfo', __PATH.SPACE_API), // 指标计划详情
  deleteTargetPlanById: getRequest('inspect/deletePlan', __PATH.SPACE_API), // 指标计划删除
  refreshTargetTemplateState: getRequest('inspectBase/refreshTemplateState', __PATH.SPACE_API), // 指标计划更新模板引用状态
  // 指标档案
  getTargetFilePageList: postRequest('inspect/selectFilesList', __PATH.SPACE_API), // 指标档案列表
  getTargetFileById: getRequest('inspect/selectFilesInfo', __PATH.SPACE_API), // 指标档案列表详情
  getTargetTrackById: postRequest('inspect/track', __PATH.SPACE_API), // 指标跟踪
  getTargetFileTree: postRequest('inspect/baseInfo', __PATH.SPACE_API), // 组织架构|空间列表|设备列表
  getTargetFileTreeId: postRequest('inspect/baseInfoChild', __PATH.SPACE_API), // 根据父级id查询子集列表
  // 指标库
  getLibraryTreeList: postRequest('inspectBase/getLibraryTree', __PATH.SPACE_API), // 指标库tree
  getLibraryPageList: postRequest('inspectBase/getLibraryPage', __PATH.SPACE_API), // 指标库列表
  // 演练类型
  getPreplanDrillTypeData: postRequest('preplanDrillType/queryDrillType', __PATH.VUE_PLAN_API), // 查询演练类型table
  deletePreplanDrillTypeData: postRequest('preplanDrillType/deleteType', __PATH.VUE_PLAN_API), // 删除演练类型
  insertPreplanDrillTypeData: postRequest('preplanDrillType/insertType', __PATH.VUE_PLAN_API), // 添加类型
  updatePreplanDrillTypeData: postRequest('preplanDrillType/updateType', __PATH.VUE_PLAN_API), // 添加类型
  // 演练统计
  getPreplanDrillStatisticaData: postRequest('preplanDrillStatistical/query', __PATH.VUE_PLAN_API), // 查询演练统计
  exportPreplanDrillStatisticaData: downFile('preplanDrillStatistical/export', __PATH.VUE_PLAN_API), // 导出演练统计
  // 演练计划
  getPreplanDrillPlanData: postRequest('preplanDrillPlan/queryPlanByPage', __PATH.VUE_PLAN_API), // 查询演练计划table
  deletePreplanDrillPlanData: postRequest('preplanDrillPlan/deletePlan', __PATH.VUE_PLAN_API), // 批量删除计划
  updatePreplanDrillPlanStateData: postRequest('preplanDrillPlan/updatePlanState', __PATH.VUE_PLAN_API), // 启用停用计划
  addPreplanDrillPlanData: postRequest('preplanDrillPlan/insertPlan', __PATH.VUE_PLAN_API), // 新增演练计划
  updatePreplanDrillPlanData: postRequest('preplanDrillPlan/updatePlan', __PATH.VUE_PLAN_API), // 新增演练计划
  getPreplanDrillPlanById: postRequest('preplanDrillPlan/getById', __PATH.VUE_PLAN_API), // 获取计划详情
  previewPreplanDrillPlanData: postRequest('preplanDrillPlan/queryTaskByPage', __PATH.VUE_PLAN_API), // 生成任务预览
  viewPreplanDrillPlanStateData: postRequest('preplanDrillTask/updatePlanState', __PATH.VUE_PLAN_API), //
  // 演练任务
  getQueryTaskData: postRequest('preplanDrillTask/queryTaskByPage', __PATH.VUE_PLAN_API), // 查询演练任务list
  getQueryTaskProgress: postRequest('preplanDrillTask/queryTaskProgress', __PATH.VUE_PLAN_API), // 查询演练任务进程
  addTaskData: postRequest('preplanDrillTask/insertTask', __PATH.VUE_PLAN_API), // 演练任务补录
  getTaskDataById: postRequest('preplanDrillTask/getTaskById', __PATH.VUE_PLAN_API), // 演练任务详情
  designateTaskData: postRequest('preplanDrillTask/updateTaskAppoint', __PATH.VUE_PLAN_API), // 演练任务
  updateTaskData: postRequest('preplanDrillTask/updateTask', __PATH.VUE_PLAN_API), // 演练任务
  uploadTask: postFile('preplanDrillTask/upload', __PATH.VUE_PLAN_API), // 演练任务
  // 总支管理
  getGeneralist: getRequest('preplanDrillGeneral/queryDrill', __PATH.VUE_PLAN_API), // 总支list
  getGeneraDeptData: postRequest('preplanDrillGeneral/queryDrillDept', __PATH.VUE_PLAN_API), // 查询总支下所有科室
  insertGeneraDeptData: postRequest('preplanDrillGeneral/insertGeneral', __PATH.VUE_PLAN_API), // 添加总支/科室
  getSelectedDeptData: getRequest('preplanDrillGeneral/queryDept', __PATH.VUE_PLAN_API), // 查询已被选择的科室
  updateGeneraDeptData: postRequest('preplanDrillGeneral/updateGeneral', __PATH.VUE_PLAN_API), // 编辑总支/科室
  deleteGeneraDeptData: postRequest('preplanDrillGeneral/deleteGeneral', __PATH.VUE_PLAN_API), // 删除总支/科室
  moveGeneraDeptData: postRequest('preplanDrillGeneral/updateGeneralDept', __PATH.VUE_PLAN_API), // 移动科室到总支
  // 应急队伍
  getEmergencyTroopManageList: postRequest('emergencyTroop/findPageEmergencyTroopManage', __PATH.VUE_PLAN_API), // 应急队伍list
  getEmergencyTroopTypeList: postParamsQS('v1/dict/getDictValues', __PATH.VUE_PLAN_API), // 应急队伍队伍类型
  addEmergencyTroopData: postRequest('emergencyTroop/saveEmergencyTroopManage', __PATH.VUE_PLAN_API), // 保存应急队伍
  deleteEmergencyTroopManageData: postParamsQS('emergencyTroop/deleteEmergencyTroopManageById', __PATH.VUE_PLAN_API), // 删除应急队伍
  getEmergencyTroopManageDeatil: postParamsQS('emergencyTroop/getEmergencyTroopManageById', __PATH.VUE_PLAN_API), // 应急队伍详情
  // 报警配置
  getAlarmConfigList: postRequest('alarmConfig/queryAlarmConfigByPage', __PATH.VUE_WARN_API), // 报警配置list
  addAlarmConfigList: postRequest('alarmConfig/insertAlarmConfig', __PATH.VUE_WARN_API), // 报警配置新增
  updateAlarmConfigList: postRequest('alarmConfig/updateAlarmConfigById', __PATH.VUE_WARN_API), // 报警配置编辑
  getAlarmConfigListById: getRequest('alarmConfig/queryAlarmDetailById', __PATH.VUE_WARN_API), // 报警配置详情
  deletAlarmConfigList: getRequest('alarmConfig/deleteAlarmConfigById', __PATH.VUE_WARN_API), // 删除报警配置
  getAlarmConfigNoticePerson: getRequest('alarmConfig/queryAlarmNoticePerson', __PATH.VUE_WARN_API), // 查询报警配置的通知方式和通知人员
  getBasicData: postRequest('preplanBasic/getBasicList', __PATH.VUE_PLAN_API), // 预案列表
  // 预案
  GetNoticePersonInfoList: postRequest('preplanTemplateBasic/getNoticePersonInfoList', __PATH.VUE_PLAN_API), // 根据科室id获取人员列表
  GetSuperiorData: getRequest('web/prDictionaryDetailsD/getSuperiorData', __PATH.VUE_SPACE_API), // 获取上级字典数据
  GetPlanTemplate: postRequest('preplanTemplateBasic/getConfigPage', __PATH.VUE_PLAN_API), // 获取预案模板列表
  SaveBasicConfig: postRequest('preplanTemplateBasic/saveBasicConfig', __PATH.VUE_PLAN_API), // 新增预案模板
  DeleteBasicConfig: postRequest('preplanTemplateBasic/deleteBasicConfig', __PATH.VUE_PLAN_API), // 删除预案模板
  GetBasicConfigDetail: postRequest('preplanTemplateBasic/getBasicConfigDetail', __PATH.VUE_PLAN_API), // 查看预案模板
  EditBasicConfig: postRequest('preplanTemplateBasic/editBasicConfig', __PATH.VUE_PLAN_API), // 编辑保存预案模板
  ExistPlanName: postRequest('preplanTemplateBasic/existPlanName', __PATH.VUE_PLAN_API), // 预案模板名称是否存在
  GetPlanList: postRequest('preplanBasic/getConfigPage', __PATH.VUE_PLAN_API), // 获取预案列表
  RepealPlan: postRequest('preplanBasic/deleteBasicConfig', __PATH.VUE_PLAN_API), // 废除预案
  SavePlanConfig: postRequest('preplanBasic/saveBasicConfig', __PATH.VUE_PLAN_API), // 保存预案
  PlanNameExist: postRequest('preplanBasic/existPlanName', __PATH.VUE_PLAN_API), // 预案名称是否存在
  GetPlanDetail: postRequest('preplanBasic/getBasicConfigDetail', __PATH.VUE_PLAN_API), // 获取预案详情
  GetHistoryPlan: postRequest('preplanBasic/getHistoryConfigPage', __PATH.VUE_PLAN_API), // 获取预案详情
  GetSaveVersion: postRequest('preplanBasic/getSaveVersion', __PATH.VUE_PLAN_API), // 获取预案版本号
  // 安全数据驾驶舱
  getHiddenManageCount: getRequest('iHCRSStatisticsController/hiddenManageCount', __PATH.VUE_IOMS_API), // 隐患上报工单各个维度统计
  getSecurityControlPersonCount: getRequest('controlGroupInfo/securityControlPersonCount', __PATH.VUE_AQ_URL), // 安全组织人员树形结构
  getTrainPlanCount: getRequest('trainPlan/count', __PATH.VUE_COURSE_URL), // 安全培训
  getTaskCountByType: postQueryQS('medicalGuard/getTaskCountByType', __PATH.VUE_ICIS_API), // 巡检各类型数据统计
  getAssetsCountByCategory: getRequest('asset/assetDetails/getAssetsCountByCategory', __PATH.VUE_ICIS_API), // 查询资产类别及其设备数量
  // 融合通信
  searchTerminalList: postRequest('communicationController/terminal/search', __PATH.VUE_CONVERGED_COM_API), // 获取终端设备列表
  addTerminal: postRequest('communicationController/terminal/add', __PATH.VUE_CONVERGED_COM_API), // 终端添加
  editTerminal: postRequest('communicationController/terminal/edit', __PATH.VUE_CONVERGED_COM_API), // 终端编辑
  deleteTerminalList: postRequest('communicationController/terminal/delete', __PATH.VUE_CONVERGED_COM_API), // 删除终端设备
  getTypeList: postRequest('communicationController/terminal/searchTerminalTypeList', __PATH.VUE_CONVERGED_COM_API), // 终端类型
  groupNumber: postRequest('communicationController/callgroup/groupno/gen', __PATH.VUE_CONVERGED_COM_API), // 频道号
  getAlarmRecordData: postRequest('communicationController/queryAlarmRecord', __PATH.VUE_CONVERGED_COM_API), // 报警纪录
  callRecordsList: postRequest('communicationController/callRecord/search', __PATH.VUE_CONVERGED_COM_API), // 呼叫记录查询
  fileQuery: postRequest('communicationController/callRecord/fileQuery', __PATH.VUE_CONVERGED_COM_API), // 通话语音视频文件查询
  addGroup: postRequest('communicationController/callgroup/add', __PATH.VUE_CONVERGED_COM_API), // 新增群组
  editGroup: postRequest('communicationController/callgroup/save', __PATH.VUE_CONVERGED_COM_API), // 编辑群组
  callgroupList: postRequest('communicationController/callgroup/search', __PATH.VUE_CONVERGED_COM_API), //  群组列表
  callgroupDetails: postRequest('communicationController/callgroup/query', __PATH.VUE_CONVERGED_COM_API), //  群组详情
  deleteCallgroupList: postRequest('communicationController/callgroup/delete', __PATH.VUE_CONVERGED_COM_API), //  删除群组
  batchDeleteCallgroupList: postRequest('communicationController/callgroup/batchDelete', __PATH.VUE_CONVERGED_COM_API), //  批量删除群组
  saveConfigData: postRequest('communicationController/config', __PATH.VUE_CONVERGED_COM_API), //  基础配置
  queryConfigData: postRequest('communicationController/queryConfig', __PATH.VUE_CONVERGED_COM_API), //  基础配置查询
  // 设备管理统计分析
  getTaskBySys: postParamsQueryString('planTaskNew/getTaskTypeList', __PATH.VUE_ICIS_API), // 设备巡检按系统统计
  getTaskByDevice: postParamsQueryString('planTaskNew/getTaskPointStatistics', __PATH.VUE_ICIS_API), // 设备保养巡检按设备统计
  getChratsByDevice: postParamsQueryString('planTaskNew/getTaskFacilityStatistics', __PATH.VUE_ICIS_API), // 按照设备统以及设备右侧饼状树状图
  getChratsByEquipment: postParamsQueryString('planTaskNew/getEquipmentTypeStatistics', __PATH.VUE_ICIS_API), // 按照设备类型饼状图
  getChratsByDepartment: postParamsQueryString('planTaskNew/getDepartmentStatistics', __PATH.VUE_ICIS_API), // 按照部门统计
  getChratsBySttaf: postParamsQueryString('planTaskNew/getPersonStatistics', __PATH.VUE_ICIS_API), // 按照人员统计
  getTaskByAsset: postParamsQueryString('asset/assetDetails/getTaskAssetList', __PATH.VUE_ICIS_API), // 巡检任务统计跳转设备列表
  getTaskListByPoint: postParamsQueryString('planTaskStatistics/findPlanTaskNewListPage', __PATH.VUE_ICIS_API), // 巡检任务统计跳转任务列表（无权限）
  exportListBySys: postParamsQueryString('planTaskStatistics/taskTypeListExport', __PATH.VUE_ICIS_API), // 设备巡检按系统导出
  getWorkOrderByTask: postParamsQueryString('planTaskStatistics/getWorkOrderList', __PATH.VUE_ICIS_API), // 巡检统计跳转工单列表
  // 事件纪录
  getEventTypeList: postRequest('alarm/record/getIncidentNameListByAlarmObjectId', __PATH.VUE_WARN_API), // 根据实体id查询事件类型列表
  getEventRecordList: postRequest('alarm/record/getAlarmRecordPageByAlarmObjectId', __PATH.VUE_WARN_API), // 根据实体id查询事件纪录列表
  getCustumParamList: postRequest('dictionary/getCustumParam', __PATH.DICTIONAR_URL), // 获取监测参数字典
  recordExcelImportLog: postFormData('surveyAndParameter/saveExcelImportLog', __PATH.VUE_IEMC_API), // 记录监测项实体导入日志
  // ==================================================================安全台账管理========================================================================
  fileDirTree: getQueryQS('SecurityLedger/queryAllSecurityLedgerMenu', __PATH.SPACE_API), // 安全台账文件夹或者分类
  dragMoveFile: postRequest('SecurityLedger/moveLedgerMenu', __PATH.SPACE_API), // 移动树节点
  addDirOrClass: postRequest('SecurityLedger/insertSecurityLedgerMenu', __PATH.SPACE_API), // 新增安全台账文件夹或者分类
  updateDirOrClass: postRequest('SecurityLedger/updateSecurityLedgerMenu', __PATH.SPACE_API), // 更新安全台账文件夹或者分类
  delDirOrClass: getQueryQS('SecurityLedger/deleteSecurityLedgerMenuById', __PATH.SPACE_API), // 删除安全台账文件夹或者分类
  fileList: postRequest('SecurityLedger/querySecurityLedgerFileByPage', __PATH.SPACE_API), // 安全台账文件列表
  addFile: postRequest('SecurityLedger/insertSecurityLedgerFile', __PATH.SPACE_API), // 新增安全台账文件
  updateFile: postRequest('SecurityLedger/updateSecurityLedgerFile', __PATH.SPACE_API), // 更新安全台账文件
  uploadFile: postFile('SecurityLedger/upload', __PATH.SPACE_API), // 上传文件
  delFile: getQueryQS('SecurityLedger/deleteSecurityLedgerFile', __PATH.SPACE_API), // 删除安全台账文件
  queryFileRecycleBinByPage: postRequest('file-recycle-bin/queryFileRecycleBinByPage', __PATH.SPACE_API), // 回收站列表查询
  completelyDelete: postRequest('file-recycle-bin/completelyDelete', __PATH.SPACE_API), // 回收站彻底删除
  preRestoreFile: postRequest('file-recycle-bin/preRestoreFile', __PATH.SPACE_API), // 回收站恢复
  restoreFileNoParents: postRequest('file-recycle-bin/restoreFileNoParents', __PATH.SPACE_API), // 回收站无父级-恢复
  queryFileAccessRecordByPage: postRequest('file-access-record/queryFileAccessRecordByPage', __PATH.SPACE_API), // 台账文件管理-访问记录分页查询
  insertFileAccessRecord: postRequest('file-access-record/insertFileAccessRecord', __PATH.SPACE_API), // 台账文件管理-访问记录新增
  yearCombo: getQueryQS('SecurityLedger/yearCombo', __PATH.SPACE_API), // 获取台账文件搜索年份
  queryUserIdByStaffId: postRequest('file-perm-relation/queryUserIdByStaffId', __PATH.SPACE_API), // 根据id查询用户
  // 字典删除
  deleteDictData: postParamsQS('sysDictData/deleteCityHospitalEqByUsed', __PATH.VUE_ICIS_API), // 隐患上报工单各个维度统计
  upsElectData: postRequest('surveyAndParameter/queryUpsElectData', __PATH.VUE_IEMC_API), // 单个电池
  upsBatteryGroupData: postRequest('surveyAndParameter/queryUpsElectSingleData', __PATH.VUE_IEMC_API), // 电池组
  upsBatteryChartsData: postRequest('surveyAndParameter/queryUpsElectSingleDataChart', __PATH.VUE_IEMC_API), // 电池组图表
  // 开门次数分析
  openDoorCount: postRequest('client/getOpenCount', __PATH.VUE_IEMC_ELEVATOR_API), // 电梯开门次数统计
  openDoorBar: postRequest('client/getOpenDoorBar', __PATH.VUE_IEMC_ELEVATOR_API), // 电梯开门次数柱状图
  exportOpenDoorList: downFile('client/exportOpenDoorList', __PATH.VUE_IEMC_ELEVATOR_API), // 电梯开门次数导出
  // 运行楼层分析
  workDuration: postRequest('runtime/queryRuntimeByTimeAndSurveyCode', __PATH.VUE_IEMC_API), // 运行时长
  stopFloorChart: postRequest('client/getStopFloorStatistics', __PATH.VUE_IEMC_ELEVATOR_API), // 电梯停靠楼层占比
  workStopFloor: postRequest('client/getSurveyStopFloorGroup', __PATH.VUE_IEMC_ELEVATOR_API), // 运行楼层停靠统计数值
  buildList: getRequest('surveyAndParameter/getElevatorRegionCode', __PATH.VUE_IEMC_ELEVATOR_API), // 获取有电梯的楼栋数据
  elecList: getRequest('client/getSurveyByRegion', __PATH.VUE_IEMC_ELEVATOR_API), // 获取有电梯的楼栋下的电梯
  runFloorList: postRequest('client/stopFloorStatisticsList', __PATH.VUE_IEMC_ELEVATOR_API), // 电梯运行楼层统计list
  runFloorExportList: downFile('client/exportStopFloorStatisticsList', __PATH.VUE_IEMC_ELEVATOR_API), // 电梯运行楼层导出
  // 报警类型配置
  getAlarmTypePageData: postRequest('alarm/alarmTypePage', __PATH.VUE_IEMC_API), // 报警配置类型分页
  insertAlarmTypeData: postRequest('alarm/insertAlarmType', __PATH.VUE_IEMC_API), // 新增报警配置类型
  updateAlarmTypeData: postRequest('alarm/updateAlarmType', __PATH.VUE_IEMC_API), // 新增报警配置类型
  getAlarmTypeParentsList: getRequest('alarm/getAlarmTypeParents', __PATH.VUE_IEMC_API), // 报警配置类型分页
  deleteAlarmTypeData: deleteFn('alarm/deleteAlarmType', __PATH.VUE_IEMC_API), // 删除报警配置类型
  // 报警配置
  getAlarmDeployPageData: postRequest('alarm/alarmDeployPage', __PATH.VUE_IEMC_API), // 报警配置分页
  getAlarmDeployTypeList: getRequest('alarm/getAlarmDeployTree', __PATH.VUE_IEMC_API), // 报警类型过滤
  changeAlarmDeployStateData: getRequest('alarm/stateAlarmDeploy', __PATH.VUE_IEMC_API), // 修改报警列表状态
  deleteAlarmDeployData: deleteFn('alarm/deleteAlarmDeploy', __PATH.VUE_IEMC_API), // 删除报警配置
  getAlarmDeployAllTypeList: getRequest('alarm/getAlarmTypeTree', __PATH.VUE_IEMC_API), // 报警类型
  insertAlarmDeployData: postRequest('alarm/insertAlarmDeploy', __PATH.VUE_IEMC_API), // 新增报警配置
  updateAlarmDeployData: postRequest('alarm/updateAlarmDeploy', __PATH.VUE_IEMC_API), // 修改报警配置
  getAlarmDeployById: getRequest('alarm/alarmDeployOne', __PATH.VUE_IEMC_API), // 查看报警配置
  getParamEntityObjectList: postRequest('iemcParamState/getParamStateAndChildByCodes', __PATH.VUE_IEMC_API), // 根据报警对象获取下拉选项
  // 统一报警配置
  getFieldsConfigByPage: postRequest('alarm/fieldsConfig/queryFieldsConfigByPage', __PATH.VUE_WARN_API), // 查询报警字典分页列表
  insertAlarmFieldConfigData: postRequest('alarm/fieldsConfig/updateAlarmDictConfig', __PATH.VUE_WARN_API), // 配置报警字典类型
  getAlarmDictConfigData: postRequest('alarm/dictConfig/queryDictConfigAll', __PATH.VUE_WARN_API), // 查询全部报警字典
  getAlarmFieldTypeData: postRequest('alarm/fieldsConfig/getAlarmFieldType', __PATH.VUE_WARN_API), // 查询统一字典类型
  // 字典配置
  getAlarmDictConfigByPage: postRequest('alarm/dictConfig/queryDictConfigByPage', __PATH.VUE_WARN_API), // 查询报警字典分页列表
  deleteAlarmDictData: getRequest('alarm/dictConfig/delete', __PATH.VUE_WARN_API), // 删除报警字典
  insertAlarmDictConfigData: postRequest('alarm/dictConfig/insert', __PATH.VUE_WARN_API), // 新增报警字典
  updateAlarmDictConfigData: postRequest('alarm/dictConfig/update', __PATH.VUE_WARN_API), // 编辑报警字典
  getAlarmDictDataById: getRequest('alarm/dictConfig/view', __PATH.VUE_WARN_API), // 报警字典详情
  // 配件二级库
  getListData: postParamsQS('materialinfo/listData', __PATH.VUE_APP_PARTS_LIBRARY), // 库存管理列表
  getMaterialTypeTree: postParamsQS('materialinfo/materialTypeTree', __PATH.VUE_APP_PARTS_LIBRARY), // 库存管理左树结构
  getWarehouseList: postParamsQS('warehouse/listData', __PATH.VUE_APP_PARTS_LIBRARY), // 仓库列表
  getLnwarehouseDetailList: postParamsQS('inwarehouseRecord/inwarehouseDetailList', __PATH.VUE_APP_PARTS_LIBRARY), // 入库明细列表
  getUnitsManageList: postParamsQS('unitsManage/lsitData', __PATH.VUE_APP_PARTS_LIBRARY), // 入库明细供应商列表
  getOutwarehouseDetailList: postParamsQS('outwarehouseRecord/outwarehouseDetailList', __PATH.VUE_APP_PARTS_LIBRARY), // 出库明细列表
  getBrandList: postParamsQS('brand/listData', __PATH.VUE_APP_PARTS_LIBRARY), // 库存预警 品牌列表
  getHospitalDictList: postParamsQS('materialsTypeSetting/hospitalDictList', __PATH.VUE_APP_PARTS_LIBRARY), // 库存预警 所属分类列表
  getInventoryNewWarningList: postParamsQS('earlyWarningInfo/inventoryNewWarningList', __PATH.VUE_APP_PARTS_LIBRARY), // 库存预警列表
  // 综合统计
  getInventoryStatistics: postParamsQueryString('materialinfo/inventoryStatistics', __PATH.VUE_APP_PARTS_LIBRARY), // 综合统计概览
  getInventoryStatisticsChart: postParamsQueryString('materialinfo/inventoryStatisticsChart', __PATH.VUE_APP_PARTS_LIBRARY), // 出入库数量统计-柱状图,折线图
  getInventoryStatisticsDetail: postParamsQueryString('materialinfo/inventoryStatisticsDetail', __PATH.VUE_APP_PARTS_LIBRARY), // 出入库数量统计 配件出入库统计信息
  getInventoryStatisticsList: postParamsQueryString('materialinfo/inventoryStatisticsList', __PATH.VUE_APP_PARTS_LIBRARY), // 配件出入库列表
  // 安防实验室
  loginLaboratory: post_formData('userLoginController/userLoginByMobile', __PATH.BASE_URL_LAB), // 实验室登录
  getDeptListLaboratory: post_json('controlGroupInfo/getControlGroupInfoList'), // 组织列表
  getControlTeamUserListLaboratory: post_json('controlTeamUser/controlTeamUser/getControlTeamUserList'), // 获取安全人员列表
  laboratoryList: post_json('laboratory/listAll'), // 实验室列表所有
  ipsmFicationlDetail: post_formData('subject/get', __PATH.BASE_URL_LABORATORY), // 分类详情
  getCourseList: post_json('course/list'), // 课程资源列表
  addCourse: post_json('course/create'), // 创建课程
  ipsmFicationlList: post_json('subject/treeAll'), // 分类列表(tree)
  subjectListAll: post_json('subject/listAll'), // 分类列表
  getCourseDetails: getHttp('course/get'), // 获取课程详情
  isUpdateCourse: getHttp('course/whether/update'), // 是否可编辑
  deleteCourse: post_formData('course/delete', __PATH.BASE_URL_LABORATORY), // 删除课程
  deleteCourseExamin: post_json('course/createDel'), // 删除课程下的试题
  getOnlineCourseList: post_json('mine/online/list'), // 线上课程列表
  getMyCourseList: post_json('mine/list'), // 我的课程列表
  collectCourse: post_formData('mine/collect', __PATH.BASE_URL_LABORATORY), // 收藏课程
  myCourseDetails: getHttp('mine/get'), // 我的课程详情
  getQuestionsList: post_json('question/list'), // 试题列表
  getListByCourseName: post_json('question/listByCourseName'), // 试题列表(题库和录入试题)
  deleteOneQuestions: post_formData('question/delete', __PATH.BASE_URL_LABORATORY), // 删除试题
  deleteQuestions: post_formData('question/delete/list', __PATH.BASE_URL_LABORATORY), // 批量删除
  allCourseList: getHttp('course/allCourse'), // 添加试题选择课程资源列表
  addQuestions: post_json('question/create'), // 逐题试题
  getQuestionsDetails: getHttp('question/get'), // 试题详情
  updateQuestions: post_json('question/update'), // 修改试题
  // downloadTemplateLaboratory: getHttp('question/template'), // 试题模板下载
  questionsList: getHttp('period/question/list'), // 根据课时id查询课后习题列表
  seeClassHour: post_formData('mine/make', __PATH.BASE_URL_LABORATORY), // 课时查看
  userAnswer: post_json('mine/make/question/submit'), // 用户答题提交
  saveUserAnswer: post_json('mine/save/question/record'), // 保存用户答题
  userAnswerInfo: getHttp('period/getUserPeriodQuestionInfo'), // 用户答题详情列表
  getExamPlanList: post_json('examinationPlan/selectByPage'), // 考试计划列表
  getExamPlanNewList: post_json('examinationPlan/selectByPageTask'), // 考试计划列表(派发)
  copyExam: post_json('examinationPlan/copy'), // 考试计划复制
  deleteExamPlan: post_json('examinationPlan/deleteByIds'), // 考试计划删除
  courseQuestionNum: post_json('examinationPlan/courseQuestionNum'), // 根据课程查询数量
  addExamPlan: post_json('examinationPlan/add'), // 考试计划新增
  examPlanDetails: post_json('examinationPlan/getInfo'), // 考试计划详情
  examPlanEdit: post_json('examinationPlan/edit'), // 考试计划编辑
  examTaskList: post_json('examinationPlan/selectTaskByPage'), // 考试任务列表
  deleteTaskByIds: post_json('examinationPlan/deleteTaskByIds'), // 考试任务删除
  getRecordInfo: post_json('examinationPlan/getRecordInfo'), // 考试任务详情
  getUserExamInfo: post_json('examinationPlan/getUserExamInfo'), // 答题试卷详情
  getExportField: post_json('examinationPlan/getExportField'), // 考试任务导出字段
  getReferenceExportField: post_json('examinationPlan/getReferenceExportField'), // 参考信息导出字段
  // 培训模板
  tarainTemplateList: post_json('trainTmp/selectByPage'), // 培训模板列表
  tarainTemplateSave: post_json('trainTmp/add'), // 培训模板新增
  tarainTemplateDetail: post_json('trainTmp/getInfo'), // 培训模板详情
  tarainTemplateDetel: post_json('trainTmp/deleteByIds'), // 培训模板删除
  tarainTemplateEdit: post_json('trainTmp/edit'), // 培训模板编辑
  tarainTemplateCopy: post_json('trainTmp/copy'), // 培训模板复制
  // 培训任务
  trainTasksList: post_json('trainPlan/selectTaskByPage'), // 培训任务列表
  trainTasksSave: post_json('trainPlan/add'), // 培训任务新增
  trainTasksEdit: post_json('trainPlan/edit'), // 培训任务编辑
  trainTasksDetail: post_json('trainPlan/getTaskInfo'), // 培训任务详情
  conferenceList: post_json('conferencePlan/selectTaskByPage'), // 会议记录列表
  subjectList: post_json('subject/listAll'), // 获取科目分类列表
  uploadMaterial: post_json('trainPlan/uploadMaterial'), // 培训任务上传资料
  conferencePlanDetail: post_json('conferencePlan/getTaskInfo'), // 会议任务详情
  conferenceUploadMaterial: post_json('conferencePlan/uploadMaterial'), // 会议任务列表上传资料
  trainPlanGetExportField: post_json('trainPlan/getExportField'), // 培训任务导出字段
  conferencePlanGetExportField: post_json('conferencePlan/getExportField'), // 会议任务列表导出字段
  trainPlanRecordEnter: post_json('trainPlan/recordEnter'), // 录入培训记录
  conferencePlanRecordEnter: post_json('conferencePlan/recordEnter'), // 录入培训记录
  // 培训计划
  confereceList: post_json('conferencePlan/selectByPage'), // 培训会议计划列表
  confereceDetail: post_json('conferencePlan/getInfo'), // 培训会议计划详情
  confereceEdit: post_json('conferencePlan/edit'), // 培训会议计划编辑
  confereceDelet: post_json('conferencePlan/deleteByIds'), // 培训会议计划删除
  confereceSave: post_json('conferencePlan/add'), // 培训会议计划新增
  conferecePublish: post_json('conferencePlan/publish'), // 发布
  trainPlanSave: post_json('trainPlan/add'), // 培训计划新增
  trainPlanEdit: post_json('trainPlan/edit'), // 培训计划编辑
  trainPlanDetal: post_json('trainPlan/getInfo'), // 培训计划详情
  trainPlanList: post_json('trainPlan/selectByPage'), // 培训计划列表
  trainPlanNewList: post_json('trainPlan/selectByPageTask'), // 培训计划列表（派发学习任务培训）
  trainPlanDelet: post_json('trainPlan/deleteByIds'), // 培训计划删除
  trainCopy: post_json('trainPlan/copy'), // 复制
  trainPublish: post_json('trainPlan/publish'), // 发布
  trainOnOrOff: post_json('trainPlan/onOrOff'), // 启用状态
  // 派发学习任务
  taskListLaboratory: post_json('learnTask/selectByPage'), // 派发学习任务列表
  distribute: post_json('learnTask/distribute'), // 派发
  deleteTaskLaboratory: post_json('learnTask/deleteByIds'), // 删除
  copyTask: post_json('learnTask/copy'), // 复制
  addTask: post_json('learnTask/add'), // 新增派发
  getTaskInfo: post_json('learnTask/getInfo'), // 获取详情
  editTask: post_json('learnTask/edit'), // 编辑
  myTaskList: post_json('learnTask/selectMyTaskByPage'), // 我的任务
  getNyTaskInfo: post_json('learnTask/getNyTaskInfo'), // 我的任务
  ipsmFicationlSave: post_json('subject/create', __PATH.BASE_URL_LABORATORY), // 分类新增
  // 准入审批
  admittanceApplyList: post_json('admittanceApply/pageQuery'), // 准入审批列表
  admittanceApplySave: post_json('admittanceApply/sendCert'), // 准入审批新增
  admittanceApplyConfirm: post_json('admittanceApply/applyConfirm'), // 审核准入申请
  admittanceApplyApplyDetailCheck: post_json('admittanceApply/applyDetailCheck'), // 审核时候:准入申请详情
  // 证书模板
  certificateList: post_json('certificate/list'), // 分页/模糊查询证书模板列表
  certificateDownload: post_json('certificate/download'), // 下载:准入证书模板
  certificateImport: post_json('certificate/import'), // 导入:准入证书模板
  certificateDelete: post_json('certificate/delete'), // 删除证书模板
  // 准入列表
  accessList: post_json('accessCriteria/list'), // 准入列表
  joinCourse: post_formData('accessCriteria/collect'), // 加入课程
  laboratorylList: post_json('laboratory/labType'), // 实验室类型
  accessDetails: post_formData('accessCriteria/queryById'), // 课单详情
  ipsmFicationlUpList: post_formData('subject/listAll', __PATH.BASE_URL_LABORATORY), // 分类新增上一级
  /**
   * 零星工程API
   */
  SporadicProject,
  // 施工作业
  ...construciton,
  // 报警配置
  getAlarmThirdSystemData: postRequest('alarm/fieldsConfig/getAlarmThirdSystemList', __PATH.VUE_WARN_API), // 查询三方报警系统
  getAlarmThirdTypeData: postRequest('alarm/fieldsConfig/queryFieldsConfigList', __PATH.VUE_WARN_API), // 查询统一字段配置列表
  /** 公租房项目 */
  rentalHousingApi: RentalHousingApi,
  // 合同管理
  contractManagementList: postRequest('houseContract/selectPageList', __PATH.VUE_RHMS_API), // 合同管理列表
  spatialInformationTree: postRequest('space-entity/queryAll', __PATH.VUE_RHMS_API), // 全部空间信息
  houseContractDetails: getQueryQS('houseContract/getById', __PATH.VUE_RHMS_API), // 获取合同详情
  billListByContractId: postRequest('bill/selectPageListByContractId', __PATH.VUE_RHMS_API), // 根据合同id查询账单分页列表
  expiringSoonCount: getQueryQS('houseContract/expiringSoonCount', __PATH.VUE_RHMS_API), // 即将过期数量
  housingResourceTreeList: postRequest('space-entity/queryAll', __PATH.VUE_RHMS_API), // 房源管理空间树
  getExtendInfoData: postRequest('fieldInfoEntity/selectList', __PATH.VUE_RHMS_API), // 房源管理扩展信息
  getAddressData: getRequest('space-entity/selectAdministrativeList', __PATH.VUE_RHMS_API), // 房源管理查询省市区
  // 监测配置
  getHikCameraData: postRequest('cameraManage/getHikCameraList', __PATH.VUE_IEMC_API), // 新增设备摄像头选择
  exportSecurityTemplateExcel: downFile('surveyAndParameter/exportSecurityExcel', __PATH.VUE_IEMC_API), // 下载监测项导入模板 门禁和摄像头设备
  importSecuritySurveyExcel: postFormData('surveyAndParameter/importSecurityExcel', __PATH.VUE_IEMC_API), // 监测实体导入 门禁和摄像头设备
  // 档案管理
  fileManagement: fileManagement,
  // 空间清查
  meetingDictEnum: postRequest('meeting/dict/getDictionaryEnum', __PATH.SPACE_API), // 字典类型
  meetingDictGetList: postRequest('meeting/dict/getList', __PATH.SPACE_API), // 会议字典列表
  getDictionariesTree: postRequest('meeting/dict/getDictionariesTree', __PATH.SPACE_API), // 会议字典列表
  meetingDictSaveOrUpdate: postRequest('meeting/dict/saveOrUpdate', __PATH.SPACE_API), // 新增或修改会议字典值
  meetingDictTree: postRequest('meeting/dict/getAllDictionaries', __PATH.SPACE_API), // 获取上级字典
  meetingDictDetail: postRequest('meeting/dict/detail', __PATH.SPACE_API), // 获取会议字典详情
  meetingDictDelete: postRequest('meeting/dict/delete', __PATH.SPACE_API), // 删除会议字典
  meetingDictStatus: postRequest('meeting/dict/status', __PATH.SPACE_API), // 启用或停用会议字典
  saveConfig: postRequest('space/config/saveConfig', __PATH.SPACE_API), // 保存审核配置信息
  getSpaceFacilityConfig: getRequest('space/config/getSpaceFacilityConfig', __PATH.SPACE_API), // 查询审核配置信息
  getSpaceFacilityRegistrationList: postRequest('space/facility/getSpaceFacilityRegistrationList', __PATH.SPACE_API), // 查询审核列表
  getFacilityRecords: postRequest('facility/record/getFacilityRecords', __PATH.SPACE_API), // 根据空间ID分页查询设备变动记录
  getDevicesList: postRequest('space/facility/getDevicesList', __PATH.SPACE_API), // 根据空间ID分页查询设备列表记录
  getSpaceFacilityRecordList: postRequest('space/facility/getSpaceFacilityRecordList', __PATH.SPACE_API), //  查询设施变动记录列表
  getUnclassifiedFacilities: postRequest('space/facility/getUnclassifiedFacilities', __PATH.SPACE_API), //  查询未分类设施列表
  specifiedType: postRequest('space/facility/specifiedType', __PATH.SPACE_API), //  指定设施类型
  getDeviceCountGroupedByFacilityType: postRequest('space/facility/getDeviceCountGroupedByFacilityType', __PATH.SPACE_API), //  查询设备总览
  processDeviceChanges: postRequest('space/facility/processDeviceChanges', __PATH.SPACE_API), //  设备变动
  reviewDeviceSubmission: postRequest('space/facility/reviewDeviceSubmission', __PATH.SPACE_API), //  设备变动
  // 督办管理
  // ---------------------------------字典配置--------------------------------------------------
  /** 获取字典类型 */
  dictGetTypeList: postRequest('olgDictType/selectDictTypeList', __PATH.VUE_IOMS_API),
  /** 分页获取字典 */
  dictQueryByPage: postFormData('olgDictData/selectDictDataList', __PATH.VUE_IOMS_API),
  /** 删除字典 */
  dictDeleteById: postRequest('olgDictData/deleteById', __PATH.VUE_IOMS_API),
  /** 新增字典 */
  dictSave: postRequest('olgDictData/save', __PATH.VUE_IOMS_API),
  /** 编辑字典 */
  dictUpdate: postRequest('olgDictData/update', __PATH.VUE_IOMS_API),
  /** 字典详情 */
  dictGetById: getRequest('olgDictData/getById', __PATH.VUE_IOMS_API),
  /** 修改字典状态 */
  dictEnableOrDisable: postRequest('olgDictData/enableOrDisable', __PATH.VUE_IOMS_API),
  /** 根据字典类型获取字典 */
  dictQueryByTypes: postRequest('olgDictData/batchQuery', __PATH.VUE_IOMS_API),
  // ---------------------------------督办配置--------------------------------------------------
  /** 获取督办列表 */
  superviseQueryByPage: postFormData('olgPushConfig/pageList', __PATH.VUE_IOMS_API),
  /** 督办新增 */
  superviseSave: postRequest('olgPushConfig/save', __PATH.VUE_IOMS_API),
  /** 督办编辑 */
  superviseUpdate: postRequest('olgPushConfig/modify', __PATH.VUE_IOMS_API),
  /** 督办详情 */
  superviseGetById: postRequest('olgPushConfig/getById', __PATH.VUE_IOMS_API),
  /** 督办删除 */
  superviseDeleteById: postRequest('olgPushConfig/delete', __PATH.VUE_IOMS_API),
  /** 检查督办规则是否存在 */
  superviseIsExist: postRequest('olgPushConfig/isExsit', __PATH.VUE_IOMS_API),
  // ---------------------------------督办配置--------------------------------------------------
  /** 督办记录列表 */
  superviseRecordQueryByPage: postFormData('olgPushRecord/pageList', __PATH.VUE_IOMS_API),
  /** 督办记录详情 */
  superviseRecordById: postRequest('olgPushRecord/getById', __PATH.VUE_IOMS_API),
  getItemList: postParamsQS('workOrderConfigController/getItemList', __PATH.VUE_IOMS_API),
  workOderType: postParamsQS('appOlgTaskManagement.do?getWorkTypeList', __PATH.VUE_IOMS_API), // 工单类型
  uploadCommon: uploadCommon(),
  getSpaceGroupList: getRequest('web/prDictionaryDetailsD/queryByPage', __PATH.VUE_SPACE_API),
  // 库房
  warehouseApi: warehouseApi,
  // 一站式接口
  oneStopApi: oneStopApi
}
