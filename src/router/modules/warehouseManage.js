/*
 * @Description:
 */
import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  // 出入库管理
  {
    path: '/warehouseOutPutManage',
    component: Layout,
    redirect: '/warehouseOutPutManage/warehouseManagement',
    name: 'warehouseOutPutManage',
    meta: {
      title: '出入库管理',
      menuAuth: '/warehouseOutPutManage'
    },
    children: [
      {
        path: 'inWarehouseManage',
        component: EmptyLayout,
        redirect: { name: 'inWarehouseManage' },
        meta: {
          title: '入库管理',
          menuAuth: '/warehouseOutPutManage/inWarehouseManage'
        },
        children: [
          {
            path: '',
            name: 'inWarehouseManage',
            component: () => import('@/views/warehouseManage/warehouseOutPutManage/inWarehouseManage/index.vue'),
            meta: {
              title: '入库管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'inWarehouseManageAdd',
            name: 'inWarehouseManageAdd',
            component: () => import('@/views/warehouseManage/warehouseOutPutManage/inWarehouseManage/inWarehouseManageAdd.vue'),
            meta: {
              title: '新建入库单',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/warehouseOutPutManage/inWarehouseManage'
            }
          },
          {
            path: 'inWarehouseManageDetails',
            name: 'inWarehouseManageDetails',
            component: () => import('@/views/warehouseManage/warehouseOutPutManage/inWarehouseManage/inWarehouseManageDetails.vue'),
            meta: {
              title: '入库单详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/warehouseOutPutManage/inWarehouseManage'
            }
          }
        ]
      },
      {
        path: 'warehouseEntryDetails',
        component: EmptyLayout,
        redirect: { name: 'warehouseEntryDetails' },
        meta: {
          title: '入库明细',
          menuAuth: '/warehouseOutPutManage/warehouseEntryDetails'
        },
        children: [
          {
            path: '',
            name: 'warehouseEntryDetails',
            component: () => import('@/views/warehouseManage/warehouseOutPutManage/warehouseEntryDetails.vue'),
            meta: {
              title: '入库明细',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'outWarehouseManage',
        component: EmptyLayout,
        redirect: '/warehouseOutPutManage/outWarehouseManage',
        meta: {
          title: '出库管理',
          menuAuth: '/warehouseOutPutManage/outWarehouseManage'
        },
        children: [
          {
            path: '',
            name: 'outWarehouseManage',
            component: () => import('@/views/warehouseManage/warehouseOutPutManage/outWarehouseManage/index.vue'),
            meta: {
              title: '出库管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'outWarehouseManageAdd',
            name: 'outWarehouseManageAdd',
            component: () => import('@/views/warehouseManage/warehouseOutPutManage/outWarehouseManage/outWarehouseManageAdd.vue'),
            meta: {
              title: '新建出库单',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/warehouseOutPutManage/outWarehouseManage'
            }
          },
          {
            path: 'outWarehouseManageDetails',
            name: 'outWarehouseManageDetails',
            component: () => import('@/views/warehouseManage/warehouseOutPutManage/outWarehouseManage/outWarehouseManageDetails.vue'),
            meta: {
              title: '出库单详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/warehouseOutPutManage/outWarehouseManage'
            }
          }
        ]
      },
      {
        path: 'outboundDetails',
        component: EmptyLayout,
        redirect: { name: 'outboundDetails' },
        meta: {
          title: '出库明细',
          menuAuth: '/warehouseOutPutManage/outboundDetails'
        },
        children: [
          {
            path: '',
            name: 'outboundDetails',
            component: () => import('@/views/warehouseManage/warehouseOutPutManage/outboundDetails.vue'),
            meta: {
              title: '出库明细',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  // 库存管理
  {
    path: '/inventoryManage',
    component: Layout,
    redirect: '/inventoryManage/stockInquiry',
    name: 'inventoryManage',
    meta: {
      title: '库存管理',
      menuAuth: '/inventoryManage'
    },
    children: [
      {
        path: 'stockInquiry',
        component: EmptyLayout,
        redirect: { name: 'stockInquiry' },
        meta: {
          title: '库存查询',
          menuAuth: '/inventoryManage/stockInquiry'
        },
        children: [
          {
            path: '',
            name: 'stockInquiry',
            component: () => import('@/views/warehouseManage/inventoryManage/stockInquiry/index.vue'),
            meta: {
              title: '库存查询',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/inventoryManage/stockInquiry'
            }
          },
          {
            path: 'inOutRecord',
            name: 'inOutRecord',
            component: () => import('@/views/warehouseManage/inventoryManage/stockInquiry/inOutRecord.vue'),
            meta: {
              title: '出入库记录详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/inventoryManage/stockInquiry'
            }
          }
        ]
      },
      {
        path: 'inventoryWarning',
        component: EmptyLayout,
        redirect: { name: 'inventoryWarning' },
        meta: {
          title: '库存预警',
          menuAuth: '/inventoryManage/inventoryWarning'
        },
        children: [
          {
            path: '',
            name: 'inventoryWarning',
            component: () => import('@/views/warehouseManage/inventoryManage/inventoryWarning/index.vue'),
            meta: {
              title: '库存预警',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/inventoryManage/inventoryWarning'
            }
          }
        ]
      },
      {
        path: 'stockInventory',
        component: EmptyLayout,
        redirect: { name: 'stockInventory' },
        meta: {
          title: '库存盘点',
          menuAuth: '/inventoryManage/stockInventory'
        },
        children: [
          {
            path: '',
            name: 'stockInventory',
            component: () => import('@/views/warehouseManage/inventoryManage/stockInventory/index.vue'),
            meta: {
              title: '库存盘点',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/inventoryManage/stockInventory'
            }
          },
          {
            path: 'addStock',
            name: 'addStock',
            component: () => import('@/views/warehouseManage/inventoryManage/stockInventory/addStock.vue'),
            meta: {
              title: '新建盘点单',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/inventoryManage/stockInventory'
            }
          }
        ]
      }
    ]
  },
  // 基础管理
  {
    path: '/basicConfig',
    component: Layout,
    redirect: '/basicConfig/warehouseManage',
    name: 'basicConfig',
    meta: {
      title: '基础管理',
      menuAuth: '/basicConfig'
    },
    children: [
      {
        path: 'warehouseManage',
        component: EmptyLayout,
        redirect: { name: 'warehouseManage' },
        meta: {
          title: '仓库管理',
          menuAuth: '/basicConfig/warehouseManage'
        },
        children: [
          {
            path: '',
            name: 'warehouseManage',
            component: () => import('@/views/warehouseManage/basicConfig/warehouseManage/index.vue'),
            meta: {
              title: '仓库管理',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/basicConfig/warehouseManage'
            }
          },
          {
            path: 'warehouseDetail',
            name: 'warehouseDetail',
            component: () => import('@/views/warehouseManage/basicConfig/warehouseManage/detail.vue'),
            meta: {
              title: '仓库详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/basicConfig/warehouseManage'
            }
          }
        ]
      },
      {
        path: 'consumablesManage',
        component: EmptyLayout,
        redirect: '/basicConfig/consumablesManage',
        meta: {
          title: '耗材管理',
          menuAuth: '/basicConfig/consumablesManage'
        },
        children: [
          {
            path: '',
            name: 'consumablesManage',
            component: () => import('@/views/warehouseManage/basicConfig/consumablesManage/index.vue'),
            meta: {
              title: '耗材管理',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/basicConfig/consumablesManage'
            }
          },
          {
            path: 'addConsumables',
            name: 'addConsumables',
            component: () => import('@/views/warehouseManage/basicConfig/consumablesManage/addConsumables.vue'),
            meta: {
              title: '耗材管理',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/basicConfig/consumablesManage'
            }
          }
        ]
      },
      {
        path: 'dictManage',
        component: EmptyLayout,
        redirect: '/basicConfig/dictManage',
        meta: {
          title: '字典管理',
          menuAuth: '/basicConfig/dictManage'
        },
        children: [
          {
            path: '',
            name: 'dictManage',
            component: () => import('@/views/warehouseManage/basicConfig/dictManage.vue'),
            meta: {
              title: '字典管理',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/basicConfig/dictManage'
            }
          }
        ]
      }
    ]
  }
]
