<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="pageLoading" class="consumables-content">
      <div class="consumables-content-title">
        <span @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          <span> {{ pageTitle }}</span>
        </span>
      </div>
      <div class="content_box">
        <el-form ref="form" :model="form" :rules="rules" label-width="auto" :hide-required-asterisk="queryParams.type === 'detail'">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="耗材名称" prop="materialName">
                <el-input v-if="queryParams.type !== 'detail'" v-model="form.materialName" :disabled="queryParams.type == 'edit'" placeholder="请输入" maxlength="30"> </el-input>
                <span v-else>{{ form.materialName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="耗材编码" prop="materialCode">
                <el-input v-if="queryParams.type !== 'detail'" v-model="form.materialCode" :disabled="queryParams.type == 'edit'" placeholder="请输入" maxlength="30"> </el-input>
                <span v-else>{{ form.materialCode }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="耗材类型" prop="materialTypeCode">
                <el-cascader
                  v-if="queryParams.type !== 'detail'"
                  ref="cascader"
                  v-model="form.materialTypeCode"
                  :options="consumablesTypeList"
                  :props="defaultProps"
                  clearable
                  filterable
                  placeholder="请选择"
                  @change="handleConsumablesTypeChange"
                ></el-cascader>
                <span v-else>{{ form.materialTypeName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="规格型号" prop="model">
                <el-input v-if="queryParams.type !== 'detail'" v-model="form.model" placeholder="请输入" maxlength="30"> </el-input>
                <span v-else>{{ form.model }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="计量单位" prop="basicUnitCode">
                <el-select v-if="queryParams.type !== 'detail'" v-model="form.basicUnitCode" placeholder="请选择" @change="handleUnitChange">
                  <el-option v-for="item in unitsList" :key="item.dictionaryDetailsId" :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsId"></el-option>
                </el-select>
                <span v-else>{{ form.basicUnitName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="品牌" prop="brandName">
                <el-input v-if="queryParams.type !== 'detail'" v-model="form.brandName" placeholder="请输入" maxlength="30"> </el-input>
                <span v-else>{{ form.brandName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="queryParams.type !== 'detail' ? '单价' : '单价（元）'" prop="unitPrice">
                <el-input
                  v-if="queryParams.type !== 'detail'"
                  v-model="form.unitPrice"
                  type="number"
                  :min="0"
                  :max="maxValue"
                  :disabled="queryParams.type == 'edit'"
                  :step="0.01"
                  placeholder="请输入"
                  :maxlength="7"
                  @change="onPriceChange('unitPrice', $event)"
                >
                  <span slot="append">元</span>
                </el-input>
                <span v-else>{{ form.unitPrice }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="24">
              <el-form-item label="图片">
                <el-upload
                  v-if="queryParams.type !== 'detail'"
                  :key="uploadKey"
                  action=""
                  list-type="picture-card"
                  class="consumables-form__upload"
                  data-type="photo"
                  :file-list="photos"
                  :accept="fileAcceptPhoto"
                  :limit="5"
                  multiple
                  :http-request="(request) => handleHttpRequest(uploadProp.photos, request)"
                  :on-change="handleFileChange"
                  :on-preview="handlePictureCardPreview"
                  :on-error="handleUploadError"
                  :on-remove="handleFileRemove"
                  @click.native="currentUploadProp = uploadProp.photos"
                >
                  <i class="el-icon-plus"></i>
                  <div slot="tip" class="el-upload__tip">单个文件大小不超过5M</div>
                </el-upload>
                <span v-else>
                  <el-image v-for="(item, index) in photos" :key="index" class="form-image" :src="item.url" :preview-src-list="srcList"> </el-image>
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-dialog :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="" />
      </el-dialog>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="onClose">取消</el-button>
      <el-button v-if="queryParams.type !== 'detail'" type="primary" :loading="formLoading" @click="onPreservation">保存</el-button>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import { transData } from '@/util'
export default {
  name: 'addConsumables',
  async beforeRouteLeave(to, from, next) {
    if (!['consumablesManage'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      // 正常表单
      form: {
        materialName: '', // 耗材名称
        materialCode: '', // 耗材编码
        model: '', // 规格型号
        basicUnitCode: '', // 计量单位
        basicUnitName: '', // 计量单位名称
        brandName: '', // 品牌
        unitPrice: '', // 单价
        materialTypeCode: [], // 耗材类型code
        materialTypeName: '', // 耗材分类名称
        materialPhoto: '', // 图片
        allPath: '' // 全路径code
      },
      photos: [],
      srcList: [],
      formLoading: false, // 表单loading
      pageLoading: false, // 初始化加载页面loading
      queryParams: {},
      unitsList: [],
      consumablesTypeList: [],
      rules: {
        materialName: [{ required: true, message: '请输入耗材名称', trigger: 'blur' }],
        materialCode: [{ required: true, message: '请输入耗材编码', trigger: 'blur' }],
        unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur' }],
        materialTypeCode: [{ required: true, message: '请选择耗材类型', trigger: 'change' }]
      },
      defaultProps: {
        label: 'dictionaryDetailsName',
        children: 'children',
        value: 'id',
        checkStrictly: true
      },
      dialogImageUrl: '',
      dialogVisible: false,
      // 当前上传组件点击后对应的文件列表key
      currentUploadProp: '',
      uploadKey: 0
    }
  },
  computed: {
    pageTitle() {
      let title = '耗材'
      switch (this.queryParams.type) {
        case 'add':
          title = '新增' + title
          break
        case 'edit':
          title = '编辑' + title
          break
        case 'detail':
          title += '详情'
          break
      }
      return title
    },
    maxValue() {
      return 99999.99
    },
    fileAcceptPhoto() {
      return '.jpg,.jpeg,.png'
    },
    // 上传组件可用的数据源key
    uploadProp() {
      return {
        photos: 'photos'
      }
    }
  },
  activated() {
    this.initEvent()
  },
  mounted() {
    this.initEvent()
  },
  methods: {
    initEvent() {
      // 重置form表单
      const data = this.$options.data()
      delete data.rules
      this.$nextTick(() => {
        Object.assign(this.$data, data)
        this.$refs.form.resetFields()
        this.getSelectByList('metering_unit', 'unitsList') // 计量单位
        this.getConsumableTypeList() // 耗材类型
        this.photos = []
        this.queryParams = this.$route.query
        if (this.queryParams.type !== 'add') {
          this.getShiftAttendanceDetail()
        }
      })
    },
    // 耗材类型名称
    handleConsumablesTypeChange(e) {
      if (e && e.length) {
        // 通过级联选择器的值直接查找对应的标签路径
        const selectedLabels = this.getLabelsFromValue(e)
        this.form.materialTypeName = selectedLabels.join('/')
      } else {
        this.form.materialTypeName = ''
      }
    },
    // 根据选中的值获取对应的标签路径
    getLabelsFromValue(values) {
      const labels = []
      let currentLevel = this.consumablesTypeList
      // 遍历每一级，查找对应的标签
      for (let i = 0; i < values.length; i++) {
        const value = values[i]
        const node = currentLevel.find((item) => item.id === value)
        if (node) {
          labels.push(node.dictionaryDetailsName)
          // 如果有子级，更新currentLevel为子级数组
          if (node.children && node.children.length > 0) {
            currentLevel = node.children
          } else {
            break
          }
        } else {
          break
        }
      }
      return labels
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleUnitChange(value) {
      const selectedUnit = this.unitsList.find((item) => item.dictionaryDetailsId === value)
      this.form.basicUnitName = selectedUnit ? selectedUnit.dictionaryDetailsName : ''
    },
    // 查询字典
    getSelectByList(str, getArr) {
      this.$api.warehouseApi.getDictByPage({ dictionaryCategoryId: str, dictionaryDetailsStatus: '1' }).then((res) => {
        if (res.code == '200') {
          this[getArr] = res.data
        }
      })
    },
    // 查询耗材类型
    getConsumableTypeList() {
      this.$api.warehouseApi.getDictByPage({ dictionaryCategoryId: 'consumables_type', dictionaryDetailsStatus: '1' }).then((res) => {
        if (res.code == '200') {
          this.consumablesTypeList = transData(res.data, 'id', 'parentId', 'children')
        }
      })
    },
    // 金额发生变化
    onPriceChange(field, val) {
      if (!val) return
      if (val < 0) {
        this.$message.error('单价不能小于0')
        this.$set(this.form, field, 0)
      } else {
        const price = Math.min(Number(val), this.maxValue).toFixed(2)
        this.$set(this.form, field, price)
      }
    },
    // 文件上传代理
    async handleHttpRequest(propKey, request) {
      const fileList = this.getFileList(propKey)
      await this.checkFile(request.file, fileList)
      const params = new FormData()
      params.append('file', request.file)
      let res = await this.$api.uploadFile(params)
      if (res.code === '200') {
        this.$message({
          message: '上传成功',
          type: 'success'
        })
        const fileInfo = fileList.find((it) => it.uid === request.file.uid)
        if (fileInfo) {
          fileInfo.uploadPath = res.data.fileKey
        }
      } else {
        throw res.message
      }
    },
    // 检测文件是否可以上传
    async checkFile(file, fileList) {
      if (/\s/.test(file.name)) {
        throw '文件名不能存在空格'
      }
      if (fileList.filter((x) => x.name === file.name).length > 1) {
        throw '已存在此文件，请重新选择'
      }
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      if (!extension.includes(extension)) {
        throw '请选择正确的文件类型'
      }
      const size = 5
      if (file.size > size * 1024 * 1024) {
        throw `文件大小不能超过${size}MB`
      }
    },
    // 失踪同步文件列表
    handleFileChange(file) {
      if (file.status === 'ready') {
        // 给文件绑定上传组件对应的数据源key
        file.prop = this.currentUploadProp
        // 对刚选择的pdf文件进行图片替换
        if (/.pdf$/i.test(file.name)) {
          file.url = iconPdf
        }
      }
      const fileList = this.getFileList(file.prop)
      if (file.status === 'fail') {
        // 失败的文件，500ms后删除，视觉效果会好一些
        window.clearTimeout(this.$timerId)
        this.$timerId = window.setTimeout(() => {
          let index = -1
          do {
            index = fileList.findIndex((it) => it.status === 'fail')
            if (index > -1) {
              fileList.splice(index, 1)
            }
          } while (index > -1)
          this.$timerId = -1
        }, 500)
      } else {
        const index = fileList.findIndex((it) => it.uid === file.uid)
        if (index > -1) {
          fileList.splice(index, 1, file)
        } else {
          fileList.push(file)
        }
      }
    },
    // 文件移除时
    handleFileRemove(file) {
      const fileList = this.getFileList(file.prop)
      const index = fileList.findIndex((it) => it.uid === file.uid)
      if (index > -1) {
        fileList.splice(index, 1)
      }
    },
    // 根据key，获取上传文件对应的数据源
    getFileList(key) {
      if (key === this.uploadProp.photos) {
        return this.photos
      } else {
        return []
      }
    },
    // 上传失败提示
    handleUploadError(err) {
      let errMsg = '上传失败'
      if (typeof err === 'string') {
        errMsg = err
      }
      this.$message.error(errMsg)
    },
    // 获取耗材详情
    getShiftAttendanceDetail() {
      this.pageLoading = true
      this.$api.warehouseApi.getConsumableById({ id: this.queryParams.id }).then((res) => {
        try {
          if (res.code == 200) {
            const data = res.data
            for (const key in this.form) {
              if (Object.prototype.hasOwnProperty.call(this.form, key)) {
                this.$set(this.form, key, data[key])
              }
            }
            if (this.queryParams.type == 'edit') {
              this.form.id = data.id
            }
            this.form.materialTypeCode = data.allPath.split(',') || data.materialTypeCode
            // 处理 materialPhoto
            if (data.materialPhoto && data.materialPhoto.split) {
              this.photos = data.materialPhoto.split(',').map((path) => this.covertPathToUploadFile(this.uploadProp.photos, path))
              this.uploadKey = Math.random() // 更新 key，强制刷新组件
              const fileEcho = this.photos.map((file) => ({ url: file.url }))
              this.srcList = fileEcho.map((item) => item.url)
            }
            this.pageLoading = false
          }
        } catch (error) {
          this.pageLoading = false
        }
      })
    },
    // 转换路径为已上传的文件
    covertPathToUploadFile(prop, path) {
      const uid = Date.now() + (Math.random() * 1000).toFixed(0)
      let url
      if (/.pdf$/i.test(path)) {
        url = iconPdf
      } else {
        url = this.$tools.imgUrlTranslation(path)
      }
      return {
        uid,
        url,
        prop,
        uploadPath: path,
        status: 'success'
      }
    },
    // 关闭
    onClose() {
      this.$router.go(-1)
    },
    // 保存
    onPreservation() {
      this.formLoading = true
      this.$refs.form.validate((valid) => {
        if (valid) {
          let uploaded = this.photos.every((it) => it.status === 'success')
          if (!uploaded) {
            throw '图片上传中'
          }
          let params = {
            ...this.form,
            userName: this.$store.state.user.userInfo.user.staffName,
            userId: this.$store.state.user.userInfo.user.staffId
          }
          if (this.queryParams.id) {
            params.id = this.queryParams.id
          }
          if (this.form.materialTypeCode && Array.isArray(this.form.materialTypeCode)) {
            params.materialTypeCode = this.form.materialTypeCode[params.materialTypeCode.length - 1]
            params.allPath = this.form.materialTypeCode.join(',')
          } else {
            params.allPath = this.form.allPath
            params.materialTypeCode = this.form.materialTypeCode
          }
          params.materialPhoto = this.photos.map((it) => it.uploadPath).join()
          this.$api.warehouseApi
            .saveOrUpdateConsumableInfo(params)
            .then((res) => {
              this.formLoading = false
              if (res.code == '200') {
                this.$message({
                  message: '保存成功',
                  type: 'success'
                })
                this.$router.go(-1)
              } else {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
              }
            })
            .catch((msg) => this.$message.error(msg || '保存失败'))
        } else {
          this.formLoading = false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.consumables-content {
  background-color: #fff;
  height: 100%;
  position: relative;
  .consumables-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
    margin-bottom: 24px;
  }
  .content_box {
    padding: 0 36px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: calc(100% - 100px);
    .el-form-item {
      .el-input,
      .el-cascader,
      .el-select {
        width: 80%;
      }
    }
    .form-image {
      width: 150px;
      height: 150px;
      margin-right: 16px;
    }
  }
}
</style>
