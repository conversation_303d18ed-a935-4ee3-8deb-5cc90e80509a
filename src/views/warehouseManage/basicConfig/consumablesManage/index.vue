<template>
  <PageContainer class="consumables-list">
    <template #content>
      <div class="consumables-list__left">
        <div class="consumables-list__left__header">
          <el-input v-model="treeSearchKeyWord" placeholder="输入关键字搜索" suffix-icon="el-icon-search" clearable></el-input>
        </div>
        <el-tree
          ref="treeRef"
          node-key="id"
          :data="treeData"
          :current-node-key="currentKey"
          :filter-node-method="filterNode"
          default-expand-all
          :props="defaultProps"
          highlight-current
          :expand-on-click-node="false"
          class="consumables-list__tree"
          @node-click="onTreeNodeClick"
        ></el-tree>
      </div>
      <div class="consumables-list__right">
        <div class="consumables-list__right__header">
          <el-form ref="formRef" :model="searchForm" class="consumables-list__search" inline @submit.native.prevent="onSearch">
            <el-form-item prop="keyWords">
              <el-input v-model="searchForm.keyWords" clearable filterable placeholder="耗材编码/耗材名称"></el-input>
            </el-form-item>
            <el-form-item prop="status">
              <el-select v-model="searchForm.status" placeholder="状态">
                <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="consumables-list__right__actions">
            <el-button type="primary" plain @click="onReset">重置</el-button>
            <el-button type="primary" @click="onSearch">搜索</el-button>
          </div>
        </div>
        <div class="consumables-list__right__operations">
          <el-button type="primary" @click="onOperate(undefined, 'add')">新增</el-button>
          <el-button type="primary" plain :loading="exportLoading" @click="onOperate(undefined, 'export')">导出</el-button>
          <el-button type="primary" plain :disabled="isEnable" @click="onOperate(undefined, 'bathEnable')">启用</el-button>
          <el-button type="danger" plain :disabled="isUnEnable" @click="onOperate(undefined, 'bathUnEnable')">停用 </el-button>
          <el-button type="danger" plain :disabled="!multipleSelection.length" @click="onOperate(undefined, 'bathDel')">删除 </el-button>
        </div>
        <div class="consumables-list__table">
          <el-table
            v-loading="tableLoadingStatus"
            height="100%"
            :data="tableData"
            border
            stripe
            table-layout="auto"
            class="tableAuto"
            row-key="id"
            @selection-change="selectionChange"
          >
            <el-table-column type="selection"> </el-table-column>
            <el-table-column label="序号" type="index" width="80">
              <template slot-scope="scope">
                <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="耗材编码" prop="materialCode" show-overflow-tooltip> </el-table-column>
            <el-table-column label="耗材名称" prop="materialName" show-overflow-tooltip></el-table-column>
            <el-table-column label="耗材类型" prop="materialTypeName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="规格型号" prop="model" show-overflow-tooltip> </el-table-column>
            <el-table-column label="计量单位" prop="basicUnitName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="品牌" prop="brandName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="单价（元）" prop="unitPriceStr" show-overflow-tooltip> </el-table-column>
            <el-table-column label="状态" prop="status">
              <template #default="{ row }">
                <span v-if="row.status == '启用'" class="consumables-list__tag--1">{{ row.status }}</span>
                <span v-else class="consumables-list__tag--0">{{ row.status }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200px">
              <template #default="{ row }">
                <el-button type="text" @click="onOperate(row, 'detail')"> 详情 </el-button>
                <el-button type="text" :disabled="row.status == '启用'" @click="onOperate(row, 'edit')"> 编辑 </el-button>
                <el-dropdown @command="(command) => onOperate(row, command)">
                  <el-button type="text" style="margin-left: 10px">更多</el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="'changeState'" :class="[row.status == '启用' ? 'delete-item' : '']">
                        {{ row.status == '停用' ? '启用' : '停用' }}
                      </el-dropdown-item>
                      <el-dropdown-item :command="'del'" class="delete-item"> 删除 </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          class="consumables-list__pagination"
          :current-page="pagination.page"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :layout="pagination.layoutOptions"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        >
        </el-pagination>
      </div>
    </template>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import { transData } from '@/util'
import axios from 'axios'
export default {
  name: 'consumablesManage',
  components: {},
  mixins: [tableListMixin],
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addConsumables'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      currentKey: '',
      treeSearchKeyWord: '',
      searchForm: {
        keyWords: '',
        status: ''
      },
      treeData: [],
      treeLoadingStatus: false,
      tableData: [],
      tableLoadingStatus: false,
      exportLoading: false, // 导出
      isEnable: true, // 启用
      isUnEnable: true, // 禁用
      multipleSelection: [],
      // 状态选项
      statusOptions: [
        {
          label: '启用',
          value: '0'
        },
        {
          label: '停用',
          value: '1'
        }
      ],
      defaultProps: {
        label: 'dictionaryDetailsName',
        children: 'children'
      }
    }
  },
  watch: {
    treeSearchKeyWord(val) {
      this.$refs.treeRef.filter(val)
    }
  },
  activated() {
    this.getTreeData()
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    // 树节点过滤
    filterNode(value, data) {
      if (!value) return true
      return data.dictionaryDetailsName.includes(value)
    },
    // 获取树数据
    getTreeData() {
      this.treeLoadingStatus = true
      this.$api.warehouseApi
        .getDictByPage({ dictionaryCategoryId: 'consumables_type', dictionaryDetailsStatus: '1' })
        .then((res) => {
          if (res.code === '200') {
            this.treeData = transData(res.data, 'id', 'parentId', 'children')
            this.getDataList()
          } else {
            throw res.data.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取字典类型失败'))
        .finally(() => {
          this.treeLoadingStatus = false
        })
    },
    selectionChange(val) {
      this.multipleSelection = val
      if (this.multipleSelection.length && this.multipleSelection.length > 0) {
        let enableShow = this.multipleSelection.every((e) => e.status == '启用')
        let unEnableShow = this.multipleSelection.every((e) => e.status == '停用')
        if (enableShow) {
          this.isUnEnable = false
        } else {
          this.isUnEnable = true
        }
        if (unEnableShow) {
          this.isEnable = false
        } else {
          this.isEnable = true
        }
      } else {
        this.isUnEnable = true
        this.isEnable = true
      }
    },
    // 搜索树
    onTreeSearch() {
      this.currentKey = ''
      this.getTreeData()
    },
    // 当树点击被点击时
    onTreeNodeClick(data) {
      this.currentKey = data.id
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 获取列表数据
    getDataList() {
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        CurrentPage: this.pagination.current,
        ...this.searchForm,
        materialTypeCode: this.currentKey
      }
      this.$api.warehouseApi
        .getConsumableByPage(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.sum
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.currentKey = ''
      this.$refs.formRef.resetFields()
      this.pagination.current = 1
      this.$refs.treeRef.setCurrentKey(null) // 清除树的选中状态
      this.getDataList()
    },
    // 列表操作相关的事件绑定
    onOperate(row, type) {
      if (['add', 'edit', 'detail'].includes(type)) {
        // 添加 编辑
        let query = type !== 'add' ? { id: row?.id ?? '' } : {}
        this.$router.push({
          path: '/basicConfig/consumablesManage/addConsumables',
          query: {
            type,
            ...query
          }
        })
      } else if (type == 'del') {
        // 删除
        if (row.status == '启用') {
          this.$message.error('启用的耗材不允许删除！')
        } else {
          this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            cancelButtonClass: 'el-button--primary is-plain'
          }).then(() => this.doDelete(row))
        }
      } else if (type == 'changeState') {
        this.doStatus(row)
      } else if (type == 'bathDel') {
        if (this.multipleSelection.some((item) => item.status != '停用')) {
          return this.$message.error('只能删除停用的耗材')
        }
        this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'el-button--primary is-plain'
        }).then(() => this.doBathDelete())
      } else if (type === 'export') {
        this.exportInfo()
      } else if (type === 'bathUnEnable') {
        this.bathEnableOrDisable('1')
      } else if (type === 'bathEnable') {
        this.bathEnableOrDisable('0')
      }
    },
    // 批量启用停用
    bathEnableOrDisable(val) {
      this.tableLoadingStatus = true
      let checkedIds = this.multipleSelection.map((it) => it.id)
      this.$api.warehouseApi
        .getConsumableStatus({ id: checkedIds.join(','), status: val })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('变更状态成功')
            this.getDataList()
          } else {
            throw res.message || '变更状态失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    },
    //
    // 导出
    exportInfo() {
      this.exportLoading = true
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        ...this.searchForm,
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        materialTypeCode: this.currentKey,
        id: this.multipleSelection.length ? this.multipleSelection.map((item) => item.id) : ''
      }
      const formData = new FormData()
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key])
      })
      axios({
        method: 'post',
        url: __PATH.BASE_URL_HSC + 'materialsConsumable/export',
        data: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          this.$message.success(res.message || '导出成功')
          this.exportLoading = false
        })
        .catch(() => {
          this.$message.error('导出失败')
          this.exportLoading = false
        })
    },
    // 批量删除
    doBathDelete() {
      this.tableLoadingStatus = true
      let checkedIds = this.multipleSelection.map((it) => it.id)
      this.$api.warehouseApi
        .delConsumableInfo({ id: checkedIds.join(',') })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('已删除')
            this.getDataList()
          } else {
            throw res.message || '删除失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    },
    // 启用停用
    doStatus(val) {
      this.tableLoadingStatus = true
      this.$api.warehouseApi
        .getConsumableStatus({ id: val.id, status: val.status == '停用' ? '0' : '1' })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('变更状态成功')
            this.getDataList()
          } else {
            throw res.message || '变更状态失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    },
    // 删除一行数据
    doDelete(val) {
      this.tableLoadingStatus = true
      this.$api.warehouseApi
        .delConsumableInfo({ id: val.id })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('已删除')
            this.getDataList()
          } else {
            throw res.message || '删除失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    }
  }
}
</script>
<style scoped lang="scss">
.consumables-list {
  ::v-deep(> .container-content) {
    display: flex;
    background-color: #fff;
  }
  &__left {
    width: 280px;
    border-right: solid 1px #eee;
    display: flex;
    flex-flow: column nowrap;
    &__header {
      padding: 16px;
      line-height: 40px;
    }
    &::v-deep(.el-tree) {
      height: calc(100% - 64px);
      overflow: auto;
      padding: 0 16px 16px 16px;
      .el-tree-node__content {
        line-height: 36px;
        height: 36px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    display: flex;
    flex-flow: column nowrap;
  }
  &__right__header {
    display: flex;
    justify-content: space-between;
    .el-form-item {
      margin-bottom: 12px;
    }
  }
  &__right__actions {
    line-height: 40px;
  }
  &__right__operations {
    padding-bottom: 16px;
  }
  &__table {
    height: calc(100% - 140px);
  }
  &__pagination {
    margin-top: 10px;
  }
  &__tag {
    // 停用
    &--0 {
      color: #f64646;
    }
    // 启用
    &--1 {
      color: #00b42a;
    }
  }
  &__table__color {
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: text-top;
    margin-right: 4px;
  }
  ::v-deep(.consumables-list__table__col-img) {
    padding: 0;
    .cell {
      padding: 4px;
    }
    .el-image {
      height: 38px;
      .el-image__error {
        background-color: transparent;
      }
    }
  }
}
// 下拉菜单中删除项的样式
::v-deep .delete-item {
  color: #f56c6c !important;
}
::v-deep .delete-item:hover {
  background-color: #fef0f0 !important;
  color: #f56c6c !important;
}
</style>
