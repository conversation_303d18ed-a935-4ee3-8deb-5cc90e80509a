<template>
  <el-dialog
    v-dialogDrag
    class="component housing-edit"
    :title="title"
    width="1000px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    destroy-on-close
    @close="onDialogClosed"
  >
    <WarehouseForm ref="formRef" />
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button v-if="!readonly" type="primary" @click="onSubmit">保存</el-button>
    </template>
  </el-dialog>
</template>
<script>
export default {
  name: 'warehouseEdit',
  components: {
    WarehouseForm: () => import('./WarehouseForm.vue')
  },
  props: {
    id: String,
    visible: Boolean,
    readonly: Boolean,
    spaceIds: Array
  },
  events: ['update:visible', 'success'],
  data: () => ({
    $timerId: -1
  }),
  computed: {
    title: function () {
      return this.id ? '修改库房' : '新增库房'
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    toFetch() {
      return this.dialogVisible && this.id
    }
  },
  watch: {
    toFetch(val) {
      val && this.getDetail()
    }
  },
  methods: {
    getDetail() {
      this.loadingStatus = true
      this.$api.warehouseApi
        .getWarehouseById({ id: this.id })
        .then((res) => {
          if (res.code === '200') {
            this.$timerId = setTimeout(() => {
              this.$refs.formRef.revertData(res.data)
            }, 100)
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取库房详情失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onDialogClosed() {
      // dialog关闭时重置form表单
      this.$refs.formRef.resetFields()
      clearTimeout(this.$timerId)
    },
    onSubmit() {
      this.$refs.formRef
        .validate()
        .then((params) => {
          params.userId = this.$store.getters.userId // 用户id
          params.userName = this.$store.getters.userName // 用户名
          if (this.id) {
            params.id = this.id
          }
          this.loadingStatus = true
          return this.$api.warehouseApi.saveOrUpdateWarehouseInfo(params)
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.message || '操作失败'
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    }
  }
}
</script>
