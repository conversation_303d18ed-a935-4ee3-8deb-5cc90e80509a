<template>
  <div class="inbound-list">
    <div class="inbound-list__header">
      <el-form ref="formRef" :model="searchForm" inline>
        <el-form-item prop="type">
          <el-select v-model="searchForm.type" placeholder="类型">
            <el-option v-for="item of typeOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="keyWords">
          <el-input v-model="searchForm.keyWords" placeholder="耗材名称/耗材编码" clearable></el-input>
        </el-form-item>
        <el-form-item prop="materialTypeCode">
          <el-cascader
            v-model="searchForm.materialTypeCode"
            :options="consumablesTypeList"
            :props="defaultProps"
            clearable
            :show-all-levels="false"
            placeholder="耗材类型"
          ></el-cascader>
        </el-form-item>
      </el-form>
      <div>
        <el-button type="primary" plain @click="onReset">重置</el-button>
        <el-button type="primary" @click="onSearch">搜索</el-button>
      </div>
    </div>
    <div class="inbound-list__actions">
      <el-button type="primary" plain :exportLoading="exportLoading" @click="handlerExport">导出</el-button>
    </div>
    <div class="inbound-list__statistics">
      <span>
        <span>库存总数</span>
        <span class="text-red">{{ totalNum }}</span>
      </span>
      <span class="price">
        <span>金额</span>
        <span class="text-red">{{ totalPrice }}</span>
      </span>
    </div>
    <div class="inbound-list__table">
      <el-table v-loading="tableLoading" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
        <el-table-column label="类型" prop="type"> </el-table-column>
        <el-table-column label="单号" prop="orderNumber">
          <template #default="{ row }">
            <span style="color: rgb(0, 138, 244); cursor: pointer" @click="toDetail(row)">{{ row.orderNumber }}</span>
          </template>
        </el-table-column>
        <el-table-column label="耗材编码" prop="materialCode" show-overflow-tooltip> </el-table-column>
        <el-table-column label="耗材名称" prop="materialName" show-overflow-tooltip></el-table-column>
        <el-table-column label="耗材类型" prop="materialTypeName" show-overflow-tooltip> </el-table-column>
        <el-table-column label="规格型号" prop="model" show-overflow-tooltip> </el-table-column>
        <el-table-column label="计量单位" prop="basicUnitName" show-overflow-tooltip> </el-table-column>
        <el-table-column label="品牌" prop="brandName" show-overflow-tooltip> </el-table-column>
        <el-table-column label="出/入库日期" prop="createTime" show-overflow-tooltip> </el-table-column>
        <el-table-column label="数量" prop="operateCount" show-overflow-tooltip> </el-table-column>
        <el-table-column label="单价" prop="unitPrice" show-overflow-tooltip> </el-table-column>
        <el-table-column label="金额" prop="sumAmountStr" show-overflow-tooltip> </el-table-column>
      </el-table>
      <el-pagination
        class="inbound-list__pagination"
        :current-page="pagination.page"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import { transData } from '@/util'
import axios from 'axios'
export default {
  name: 'InboundRecords',
  mixins: [tableListMixin],
  props: {
    id: String
  },
  data: () => ({
    tableLoading: false,
    tableData: [],
    searchForm: {
      keyWords: '', // 耗材名称/耗材编码
      materialTypeCode: '', // 耗材类型
      type: '' // 出库类型
    },
    consumablesTypeList: [],
    defaultProps: {
      label: 'dictionaryDetailsName',
      children: 'children',
      value: 'id',
      checkStrictly: true
    },
    totalNum: 0,
    totalPrice: 0,
    // 类型选项
    typeOptions: [
      {
        label: '出库',
        value: 'WZCK'
      },
      {
        label: '入库',
        value: 'WZRK'
      }
    ],
    exportLoading: false
  }),
  mounted() {
    this.getConsumableTypeList()
    this.getDataList()
  },
  methods: {
    // 查询耗材类型
    getConsumableTypeList() {
      this.$api.warehouseApi.getDictByPage({ dictionaryCategoryId: 'consumables_type', dictionaryDetailsStatus: '1' }).then((res) => {
        if (res.code == '200') {
          this.consumablesTypeList = transData(res.data, 'id', 'parentId', 'children')
        }
      })
    },
    // 获取列表数据
    getDataList() {
      this.tableLoading = true
      const { materialTypeCode, keyWords, type } = this.searchForm
      const params = {
        pageSize: this.pagination.size,
        CurrentPage: this.pagination.current,
        warehouseId: this.id,
        type,
        keyWords,
        materialTypeCode: Array.isArray(materialTypeCode) ? materialTypeCode[materialTypeCode.length - 1] : ''
      }
      this.$api.warehouseApi
        .getInOutListByPage(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.sum
            this.totalNum = res.data.sumCount || 0
            this.totalPrice = res.data.sumAmount || 0
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.pagination.current = 1
      this.getDataList()
    },
    // 导出
    handlerExport() {
      this.exportLoading = true
      const userInfo = this.$store.state.user.userInfo.user
      const { materialTypeCode, keyWords, type } = this.searchForm
      const params = {
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        warehouseId: this.id,
        type,
        keyWords,
        materialTypeCode: Array.isArray(materialTypeCode) ? materialTypeCode[materialTypeCode.length - 1] : ''
      }
      const formData = new FormData()
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key])
      })
      axios({
        method: 'post',
        url: __PATH.BASE_URL_HSC + 'warehouse/exportInOutList',
        data: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          this.$message.success(res.message || '导出成功')
          this.exportLoading = false
        })
        .catch(() => {
          this.$message.error('导出失败')
          this.exportLoading = false
        })
    },
    toDetail(row) {
      this.$router.push({ name: 'inWarehouseManageDetails', query: { type: 'detail', recordNumber: row.orderNumber } })
    }
  }
}
</script>
<style lang="scss" scoped>
.inbound-list {
  height: 100%;
  &__header {
    display: flex;
    justify-content: space-between;
  }
  .el-form-item {
    margin-bottom: 16px;
  }
  &__table {
    margin-top: 16px;
    height: calc(100% - 230px);
  }
  &__pagination {
    margin-top: 16px;
  }
  &__statistics {
    margin-top: 16px;
    .price {
      margin-left: 16px;
    }
    .text-red {
      color: red;
      font-weight: bold;
      margin-left: 8px;
    }
  }
}
</style>
