<template>
  <el-dialog
    v-dialogDrag
    class="component dictionary-value-edit"
    :title="title"
    width="30%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    @closed="onDialogClosed"
  >
    <el-form ref="formRef" :model="formModel" :rules="rules" label-width="95px" :disabled="readonly" :class="{ readonly }">
      <el-row :gutter="12">
        <el-col :span="24">
          <el-form-item v-if="dictType === 'consumables_type'" label="上级字典" prop="parentIds">
            <el-cascader
              ref="cascaderRef"
              v-model="formModel.parentIds"
              :options="dictData"
              :props="{ label: 'name', checkStrictly: true, value: 'id' }"
              :placeholder="readonly ? '-' : '请选择'"
              clearable
            >
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="字典值名称" prop="name">
            <el-input v-model="formModel.name" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="字典值编码" prop="code">
            <el-input v-model="formModel.code" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="排序" prop="sort">
            <el-input v-model="formModel.sort" placeholder="请输入" onkeyup="value=value.replace(/^0|\D/,'')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="启用状态" prop="status">
            <el-select v-model="formModel.status" placeholder="请选择">
              <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button v-if="!readonly" type="primary" :loading="loadingStatus" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import { transData } from '@/util'
export default {
  name: 'DictionaryValueEdit',
  props: {
    visible: Boolean,
    id: String,
    readonly: Boolean,
    dictType: String,
    rowData: Object
  },
  events: ['update:visible', 'success'],
  data: function () {
    return {
      formModel: {
        name: '',
        code: '',
        status: 1,
        sort: '',
        parentIds: ''
      },
      rules: {
        name: [{ required: true, message: '请输入字典值名称' }],
        code: [{ required: true, message: '请输入字典值编码' }],
        status: [{ required: true, message: '请选择字典值状态' }]
      },
      dictData: [],
      loadingStatus: false,
      statusOptions: UsingStatusOptions
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    title() {
      if (this.readonly) {
        return '查看字典值'
      } else {
        return !this.id ? '新增字典值' : '编辑字典值'
      }
    },
    toFetch() {
      return this.dialogVisible
    }
  },
  watch: {
    // 监测dialog打开就获取一次字典
    dialogVisible(value) {
      if (!value) return
      this.$refs.formRef?.resetFields()
      const params = {
        dictionaryCategoryId: this.dictType,
        dictionaryDetailsStatus: '1'
      }
      this.$api.warehouseApi.getDictByPage(params).then((res) => {
        if (res.code === '200') {
          this.dictData = transData(res.data, 'id', 'parentId', 'children')
        }
      })
    },
    toFetch(value) {
      if (value) {
        this.getDetail()
      }
    }
  },
  methods: {
    // 反显
    getDetail() {
      this.formModel.code = this.rowData.dictionaryDetailsCode || ''
      this.formModel.name = this.rowData.dictionaryDetailsName || ''
      this.formModel.sort = this.rowData.dictionaryDetailsSort || ''
      this.formModel.status = this.rowData.dictionaryDetailsStatus ? Number(this.rowData.dictionaryDetailsStatus) : 1
      this.formModel.sort = this.rowData.dictionaryDetailsSort || ''
      this.formModel.parentIds = this.rowData.parentId || ''
    },
    // dialog点击右上角关闭按钮，重置表单
    onDialogClosed() {
      this.$refs.formRef.resetFields()
    },
    // 表单提交
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          this.loadingStatus = true
          const materialParentIdMap = {
            consumables_type: '44401286157123132762',
            store_type: '44401286157123132763',
            outbound_type: '44401286157123132764',
            metering_unit: '44401286157123132765'
          }
          const params = {
            materialParentId: this.formModel.parentIds,
            materialTypeName: this.formModel.name, // 字典名称
            materialTypeCode: this.formModel.code, // 	字典编码
            sort: this.formModel.sort, // 排序
            dictionaryDetailsStatus: String(this.formModel.status), // 状态 0-停用 1-启用
            dictionaryCategoryId: this.dictType
          }
          if (Array.isArray(params.materialParentId) && params.materialParentId.length) {
            params.materialParentId = params.materialParentId[params.materialParentId.length - 1]
          } else if (typeof params.materialParentId === 'string' && params.materialParentId) {
            // 处理字符串情况，或保持原值
            params.materialParentId = params.materialParentId
          } else {
            params.materialParentId = materialParentIdMap[this.dictType]
          }
          if (this.id) {
            params.id = this.id
            params.oldParentId = this.id
            params.dictionaryDetailsName = this.rowData.materialTypeName
            params.dictionaryDetailsCode = this.rowData.materialTypeCode
            params.dictionaryDetailsSort = this.rowData.dictionaryDetailsStatus
            params.parentId = params.materialParentId
            return this.$api.warehouseApi.updateDictInfo(params)
          } else {
            return this.$api.warehouseApi.saveDictInfo(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    }
  }
}
</script>
<style lang="scss">
.component.dictionary-value-edit {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-input,
      .el-cascader,
      .el-select {
        width: 100%;
      }
    }
    &.readonly {
      .el-form-item__label::before {
        display: none;
      }
      .el-form-item {
        .el-input__inner,
        .el-cascader,
        .el-select {
          background-color: transparent;
          border: none;
          color: #121f3e;
        }
        .el-input__suffix {
          display: none;
        }
        .el-upload-list__item-status-label {
          display: none;
        }
        .el-upload__tip {
          display: none;
        }
      }
    }
  }
  .dictionary-value-edit {
    &__color {
      vertical-align: middle;
      .el-input-group__prepend {
        padding: 0;
        line-height: 0;
        border-radius: 4px 0 0 4px;
      }
      .el-color-picker {
        height: 30px;
      }
      .el-color-picker__trigger {
        border: none;
        height: 30px;
        width: 30px;
        padding: 2px;
      }
      .el-input__inner {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
  }
}
</style>
