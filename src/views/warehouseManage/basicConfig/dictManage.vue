<template>
  <PageContainer class="dictionary-list">
    <template #content>
      <div class="dictionary-list__left">
        <el-tree
          ref="treeRef"
          node-key="id"
          :data="treeData"
          :current-node-key="currentKey"
          :expand-on-click-node="false"
          default-expand-all
          highlight-current
          class="dictionary-list__tree"
          @node-click="onTreeNodeClick"
        ></el-tree>
      </div>
      <div class="dictionary-list__right">
        <div class="dictionary-list__right__header">
          <el-form ref="formRef" :model="searchForm" class="dictionary-list__search" inline @submit.native.prevent="onSearch">
            <el-form-item prop="name">
              <el-input v-model="searchForm.name" clearable filterable placeholder="搜索名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" plain @click="onReset">重置</el-button>
              <el-button type="primary" @click="onSearch">搜索</el-button>
            </el-form-item>
          </el-form>
          <div class="dictionary-list__right__actions">
            <el-button type="primary" @click="onOperate(undefined, OperateType.Create)">新增</el-button>
          </div>
        </div>
        <div class="dictionary-list__table">
          <el-table
            v-loading="tableLoadingStatus"
            height="100%"
            :data="tableData"
            border
            stripe
            table-layout="auto"
            class="tableAuto"
            row-key="id"
            :tree-props="{ children: 'children' }"
          >
            <el-table-column label="字典名称" prop="name" show-overflow-tooltip></el-table-column>
            <el-table-column label="编码" prop="code" show-overflow-tooltip> </el-table-column>
            <el-table-column v-if="currentKey === 'consumables_type'" label="上级字典" prop="parentName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="排序" prop="dictionaryDetailsSort" show-overflow-tooltip> </el-table-column>
            <el-table-column label="状态" prop="status">
              <template #default="{ row }">
                <span class="dictionary-list__tag" :class="`dictionary-list__tag--${row.dictionaryDetailsStatus}`">
                  {{ row.dictionaryDetailsStatus | statusFilter }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200px">
              <template #default="{ row }">
                <el-button type="text" @click="onOperate(row, OperateType.Edit)"> 编辑 </el-button>
                <el-button v-if="currentKey === 'consumables_type'" type="text" @click="onOperate(row, OperateType.Child)"> 新增下级 </el-button>
                <el-dropdown @command="(command) => onOperate(row, command)">
                  <el-button type="text" style="margin-left: 10px">更多</el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="OperateType.View"> 查看 </el-dropdown-item>
                      <el-dropdown-item :command="OperateType.Delete" class="delete-item"> 删除 </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <!--字典值编辑-->
      <DictionaryValueEdit :visible.sync="dialog.show" v-bind="dialog" :dictType="currentKey" :rowData="dialog.rowData" @success="getDataList" />
    </template>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import { UsingStatusOptions } from '@/views/operationPort/constant'
import { transData } from '@/util'
export default {
  name: 'DictionaryList',
  components: {
    DictionaryValueEdit: () => import('./components/DictionaryValueEdit')
  },
  filters: {
    statusFilter(status) {
      return UsingStatusOptions.find((it) => it.value == status)?.label ?? '-'
    }
  },
  mixins: [tableListMixin],
  data() {
    return {
      currentKey: 'consumables_type',
      searchForm: {
        name: ''
      },
      treeData: [],
      treeLoadingStatus: false,
      tableData: [],
      tableLoadingStatus: false,
      dialog: {
        show: false,
        id: '',
        readonly: false, // 查看模式
        rowData: {}
      }
    }
  },
  computed: {
    OperateType() {
      return {
        Edit: 'edit',
        Delete: 'delete',
        Create: 'create',
        View: 'view',
        Child: 'child'
      }
    }
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    // 获取树数据
    getTreeData() {
      this.treeLoadingStatus = true
      Promise.resolve()
        .then(() => {
          this.treeData = [
            {
              id: 'consumables_type',
              label: '耗材类型'
            },
            {
              id: 'store_type',
              label: '入库类型'
            },
            {
              id: 'outbound_type',
              label: '出库类型'
            },
            {
              id: 'metering_unit',
              label: '计量单位'
            }
          ]
          const [first] = this.treeData
          if (first) {
            this.currentKey = first.id
            this.onSearch()
            this.$nextTick(() => {
              this.$refs.treeRef.setCurrentKey(first.id)
            })
          }
        })
        .finally(() => {
          this.treeLoadingStatus = false
        })
    },
    // 搜索树
    onTreeSearch() {
      this.currentKey = ''
      this.getTreeData()
    },
    // 当树点击被点击时
    onTreeNodeClick(data) {
      if (data.id !== this.currentKey) {
        this.currentKey = data.id
        this.onReset()
      }
    },
    // 获取列表数据
    getDataList() {
      this.tableLoadingStatus = true
      const params = {
        // pageSize: this.pagination.size,
        // currentPage: this.pagination.current,
        materialName: this.searchForm.name,
        userType: 1,
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId,
        dictionaryCategoryId: this.currentKey,
        dictionaryDetailsStatus: ''
      }
      this.$api.warehouseApi
        .getDictByPage(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableData = transData(res.data, 'id', 'pid', 'children')
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 列表操作相关的事件绑定
    onOperate(row, command) {
      switch (command) {
        case this.OperateType.Delete:
          // 删除非启用的字典数据
          if (row.dictionaryDetailsStatus === '1') {
            this.$message.error('启用的字典不允许删除！')
          } else {
            this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            }).then(() => this.doDelete(row))
          }
          break
        case this.OperateType.Child:
          this.dialog.show = true
          this.dialog.id = ''
          this.dialog.readonly = false
          this.dialog.rowData = {
            parentId: row.id
          }
          break
        case this.OperateType.Create:
          this.dialog.show = true
          this.dialog.id = ''
          this.dialog.readonly = false
          this.dialog.rowData = {}
          break
        default:
          this.dialog.id = row?.id
          this.dialog.readonly = command === this.OperateType.View
          this.dialog.rowData = { ...row }
          this.dialog.show = true
          break
      }
    },
    // 删除一行数据
    doDelete(val) {
      this.tableLoadingStatus = true
      this.$api.warehouseApi
        .delDictInfo({ dictionaryDetailsId: val.id, parentId: val.parentId })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('已删除')
            this.getDataList()
          } else {
            throw res.message || '删除失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    }
  }
}
</script>
<style scoped lang="scss">
.dictionary-list {
  ::v-deep(> .container-content) {
    display: flex;
    background-color: #fff;
  }
  &__left {
    width: 280px;
    border-right: solid 1px #eee;
    display: flex;
    flex-flow: column nowrap;
    &::v-deep(.el-tree) {
      height: calc(100% - 64px);
      overflow: auto;
      padding: 16px;
      .el-tree-node__content {
        line-height: 36px;
        height: 36px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    display: flex;
    flex-flow: column nowrap;
  }
  &__right__header {
    display: flex;
    justify-content: space-between;
  }
  &__right__actions {
    line-height: 40px;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
  &__table__color {
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: text-top;
    margin-right: 4px;
  }
  ::v-deep(.dictionary-list__table__col-img) {
    padding: 0;
    .cell {
      padding: 4px;
    }
    .el-image {
      height: 38px;
      .el-image__error {
        background-color: transparent;
      }
    }
  }
}
// 下拉菜单中删除项的样式
::v-deep .delete-item {
  color: #f56c6c !important;
}
::v-deep .delete-item:hover {
  background-color: #fef0f0 !important;
  color: #f56c6c !important;
}
</style>
