<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <div class="left">
          <el-input v-model="searchForm.keyWords" placeholder="盘点单号/盘点单名称" clearable style="width: 200px"></el-input>
          <el-select v-model="searchForm.warehouseId" placeholder="仓库" filterable clearable>
            <el-option v-for="item of warehouseList" :key="item.id" :value="item.id" :label="item.warehouseName"></el-option>
          </el-select>
          <el-select v-model="searchForm.status" placeholder="盘点状态">
            <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
          <el-date-picker
            v-model="searchForm.time"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            style="width: 300px"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </div>
        <div class="right">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">搜索</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="table-content">
      <div class="btn-list">
        <el-button type="primary" icon="el-icon-plus" @click="control('add')">新建</el-button>
        <el-button type="primary" plain :exportLoading="exportLoading" @click="handlerExport">导出</el-button>
        <el-button type="danger" plain :disabled="!multipleSelection.length" @click="control('bathDel')">删除 </el-button>
      </div>
      <el-table
        v-loading="tableLoading"
        height="calc(100% - 110px)"
        :data="tableData"
        border
        stripe
        table-layout="auto"
        class="tableAuto"
        row-key="id"
        @selection-change="selectionChange"
      >
        <el-table-column type="selection"> </el-table-column>
        <el-table-column label="序号" type="index" width="80">
          <template slot-scope="scope">
            <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="盘点单号" prop="takeStockCode">
          <template #default="{ row }">
            <span style="color: rgb(0, 138, 244); cursor: pointer" @click="control('detail', row)">{{ row.takeStockCode }}</span>
          </template>
        </el-table-column>
        <el-table-column label="盘点单名称" prop="takeStockName" show-overflow-tooltip> </el-table-column>
        <el-table-column label="盘点仓库" prop="warehouseName" show-overflow-tooltip> </el-table-column>
        <el-table-column label="盘点状态" prop="status">
          <template #default="{ row }">
            <span>{{ row.status }}</span>
          </template>
        </el-table-column>
        <el-table-column label="完成度" prop="completion" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.completion * 100 || '0.00' }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="计划日期" prop="planDate" show-overflow-tooltip width="220">
          <template #default="{ row }">
            <span>{{ row.planStartDate }} 至 {{ row.planEndDate }}</span>
          </template>
        </el-table-column>
        <el-table-column label="责任人" prop="responsiblePersonName" show-overflow-tooltip> </el-table-column>
        <el-table-column label="完成日期" prop="finshTime" show-overflow-tooltip> </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button type="text" :disabled="row.status !== '未开始'" @click="control('edit', row)"> 修改 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="warehouseManage-list__pagination"
        :current-page="pagination.current"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      >
      </el-pagination>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import tableListMixin from '@/mixins/tableListMixin'
import { transData } from '@/util'
import axios from 'axios'
export default {
  name: 'stockInventory',
  components: {},
  mixins: [tableListMixin],
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addStock'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      tableLoading: false,
      searchForm: {
        keyWords: '',
        status: '',
        time: [],
        warehouseId: ''
      },
      tableData: [],
      pagination: {
        current: 1,
        size: 15,
        total: 0
      },
      multipleSelection: [],
      // 状态选项
      statusOptions: [
        {
          label: '未开始',
          value: '0'
        },
        {
          label: '进行中',
          value: '1'
        },
        {
          label: '已完成',
          value: '2'
        }
      ],
      warehouseList: [],
      exportLoading: false
    }
  },
  activated() {
    this.getWarehouseData()
    this.getDataList()
  },
  mounted() {
    this.getWarehouseData()
    this.getDataList()
  },
  methods: {
    // 获取树数据
    getWarehouseData() {
      let param = {
        pageSize: 99999,
        CurrentPage: 1,
        status: '0'
      }
      this.$api.warehouseApi
        .getWarehouseByPage(param)
        .then((res) => {
          if (res.code == 200) {
            this.warehouseList = res.data.list
          }
        })
        .catch((msg) => this.$message.error(msg || '获取仓库失败'))
    },
    selectionChange(val) {
      this.multipleSelection = val
    },
    // 查询
    search() {
      this.getDataList()
    },
    // 重置查询
    resetForm() {
      this.pagination.current = 1
      Object.assign(this.$data.searchForm, this.$options.data().searchForm)
      this.getDataList()
    },
    control(type, row) {
      if (['add', 'edit', 'detail'].includes(type)) {
        // 添加 编辑
        let query = type !== 'add' ? { id: row?.id ?? '' } : {}
        this.$router.push({
          path: '/inventoryManage/stockInventory/addStock',
          query: {
            type,
            ...query
          }
        })
      } else if (type == 'bathDel') {
        if (this.multipleSelection.some((item) => item.status != '未开始')) {
          return this.$message.error('只能删除未开始的盘点单')
        }
        this.$confirm('删除后将无法恢复，是否确定删除？', '盘点删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'el-button--primary is-plain',
          type: 'warning'
        }).then(() => this.doBathDelete())
      }
    },
    // 批量删除
    doBathDelete() {
      this.tableLoadingStatus = true
      let checkedIds = this.multipleSelection.map((it) => it.id)
      this.$api.warehouseApi
        .deTakeStockInfo({ ids: checkedIds.join(',') })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('已删除')
            this.getDataList()
          } else {
            throw res.message || '删除失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    },
    // 获取盘点单列表
    getDataList() {
      const { warehouseId, keyWords, status, time } = this.searchForm
      let params = {
        size: this.pagination.size,
        current: this.pagination.current,
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId,
        userDeptId: this.$store.state.user.userInfo.user.deptId,
        startTime: time.length ? time[0] : '',
        endTime: time.length ? time[1] : '',
        keyWords,
        warehouseId,
        status
      }
      this.tableLoading = true
      this.$api.warehouseApi
        .getTakeStockByPage(params)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.list
            this.pagination.total = res.data.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 导出
    handlerExport() {
      this.exportLoading = true
      const { warehouseId, keyWords, status, time } = this.searchForm
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        size: this.pagination.size,
        current: this.pagination.current,
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId,
        userDeptId: this.$store.state.user.userInfo.user.deptId,
        id: this.multipleSelection.map((it) => it.id).join(','),
        startTime: time.length ? time[0] : '',
        endTime: time.length ? time[1] : '',
        keyWords,
        warehouseId,
        status
      }
      axios({
        method: 'post',
        url: __PATH.BASE_URL_HSC + 'takeStock/exportTakeStockList',
        data: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          this.$message.success(res.message || '导出成功')
          this.exportLoading = false
        })
        .catch(() => {
          this.$message.error('导出失败')
          this.exportLoading = false
        })
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pagination.total - deleteNum) / this.pagination.size)
      let currentPage = this.pagination.current > deleteAfterPage ? deleteAfterPage : this.pagination.current
      this.pagination.current = currentPage < 1 ? 1 : currentPage
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 16px !important;
  .search-from {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      align-items: center;
      & > div {
        margin-right: 10px;
      }
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  .el-table {
    margin: 16px 0;
  }
  &__tag {
    // 停用
    &--0 {
      color: #f64646;
    }
    // 启用
    &--1 {
      color: #00b42a;
    }
  }
}
::v-deep .el-date-editor .el-range__icon {
  line-height: 28px !important;
}
::v-deep .el-range-editor.el-input__inner {
  padding: 1px 10px;
}
</style>
