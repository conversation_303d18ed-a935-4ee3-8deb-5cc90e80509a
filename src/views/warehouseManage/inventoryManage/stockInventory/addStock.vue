<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="pageLoading" class="stockInventory-content">
      <div class="stockInventory-content-title">
        <span @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          <span> {{ pageTitle }}</span>
        </span>
      </div>
      <div class="content_box">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px" :hide-required-asterisk="queryParams.type === 'detail'">
          <el-row :gutter="20">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              <span>单据信息</span>
            </div>
            <el-col :span="8">
              <el-form-item label="盘点单号" prop="takeStockCode">
                <el-input v-if="queryParams.type !== 'detail'" v-model="form.takeStockCode" disabled="" placeholder="自动生成" maxlength="50"> </el-input>
                <span v-else>{{ form.takeStockCode }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="盘点单名称" prop="takeStockName">
                <el-input v-if="queryParams.type !== 'detail'" v-model="form.takeStockName" placeholder="请输入" maxlength="30"> </el-input>
                <span v-else>{{ form.takeStockName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="计划期限" prop="time">
                <el-date-picker
                  v-if="queryParams.type !== 'detail'"
                  v-model="form.time"
                  type="datetimerange"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                >
                </el-date-picker>
                <span v-else>{{ form.planStartDate }}至{{ form.planEndDate }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="责任部门" prop="responsibleDepartmentId">
                <el-cascader
                  v-if="queryParams.type !== 'detail'"
                  ref="manageUnitCascader"
                  v-model="form.responsibleDepartmentId"
                  placeholder="请选择"
                  :options="responsibleUnitOptions"
                  :props="manageUnitProps"
                  clearable
                  filterable
                  @change="selectManageUnit"
                >
                </el-cascader>
                <span v-else>{{ form.responsibleDepartmentName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="责任人" prop="responsiblePersonId">
                <el-select v-if="queryParams.type !== 'detail'" v-model="form.responsiblePersonId" filterable clearable placeholder="请选择" @change="selectManagerPerson">
                  <el-option v-for="item in personList" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
                </el-select>
                <span v-else>{{ form.responsiblePersonName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="盘点仓库" prop="warehouseId">
                <el-select v-if="queryParams.type !== 'detail'" v-model="form.warehouseId" placeholder="仓库" filterable multiple clearable @change="warehouseChange">
                  <el-option v-for="item of warehouseList" :key="item.id" :value="item.id" :label="item.warehouseName"></el-option>
                </el-select>
                <span v-else>{{ form.warehouseName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="盘点耗材类型" prop="materialTypeCode">
                <el-cascader
                  v-if="queryParams.type !== 'detail'"
                  ref="cascader"
                  v-model="form.materialTypeCode"
                  :options="consumablesTypeList"
                  :props="materialTypeProps"
                  filterable
                  size="mini"
                  clearable
                  :show-all-levels="false"
                  placeholder="请选择"
                  @change="handleConsumablesTypeChange"
                ></el-cascader>
                <span v-else>{{ form.materialTypeName }}</span>
              </el-form-item>
            </el-col>
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              <span>盘点耗材明细</span>
            </div>
            <el-col :span="24">
              <el-button v-if="queryParams.type !== 'detail'" type="primary" @click="addConsumables">新增</el-button>
              <el-button v-if="queryParams.type !== 'detail'" type="danger" @click="handleBatchDelete()">批量删除</el-button>
              <div>
                <el-table
                  :key="queryParams.type"
                  :data="tableData"
                  stripe
                  height="300px"
                  style="width: 100%; margin-top: 16px"
                  row-key="uniqueId"
                  border
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column v-if="queryParams.type !== 'detail'" type="selection"> </el-table-column>
                  <el-table-column type="index" label="序号"> </el-table-column>
                  <el-table-column prop="warehouseName" label="仓库名称" show-overflow-tooltip> </el-table-column>
                  <el-table-column prop="materialTypeName" label="耗材类型" show-overflow-tooltip> </el-table-column>
                  <el-table-column prop="materialCode" label="耗材编码" show-overflow-tooltip> </el-table-column>
                  <el-table-column prop="materialName" label="耗材名称" show-overflow-tooltip> </el-table-column>
                  <el-table-column prop="model" label="规格型号" show-overflow-tooltip> </el-table-column>
                  <el-table-column prop="basicUnitName" label="计量单位" show-overflow-tooltip> </el-table-column>
                  <el-table-column prop="brandName" label="品牌" show-overflow-tooltip> </el-table-column>
                  <el-table-column v-if="queryParams.type == 'detail'" prop="status" label="盘点状态" show-overflow-tooltip>
                    <template #default="{ row }">
                      <span>{{ row.status == 0 ? '未盘点' : '已盘点' }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column v-if="queryParams.type === 'detail'" prop="takeStockNum" label="盘点时库存数量" show-overflow-tooltip> </el-table-column>
                  <el-table-column v-if="queryParams.type === 'detail'" prop="actualNum" label="实际盘点数量" show-overflow-tooltip> </el-table-column>
                  <el-table-column v-if="queryParams.type === 'detail'" prop="discrepancy" label="盘点差额" show-overflow-tooltip> </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-row>
        </el-form>
        <selectConsumables
          v-if="consumablesVisible"
          :visible.sync="consumablesVisible"
          :selected-data="[...tableData]"
          :info="{ warehouseId: form.warehouseId, materialTypeCode: form.materialTypeCode }"
          @handleConsumablesSelect="handleConsumablesSelect"
          @closeDialog="closeDialog"
        ></selectConsumables>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="onClose">取消</el-button>
      <el-button v-if="queryParams.type !== 'detail'" type="primary" :loading="formLoading" @click="onPreservation">保存</el-button>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import selectConsumables from './components/selectConsumables.vue'
import { transData } from '@/util'
export default {
  name: 'addStock',
  components: {
    selectConsumables
  },
  async beforeRouteLeave(to, from, next) {
    if (!['stockInventory'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      // 正常表单
      form: {
        takeStockName: '', // 耗材名称
        takeStockCode: '', // 耗材编码
        time: [], // 计划期限
        planStartDate: '', // 计划开始时间
        planEndDate: '', // 计划结束时间
        warehouseId: [], // 仓库id
        warehouseName: '', // 仓库名称
        responsibleDepartmentId: [], // 责任部门id
        responsibleDepartmentName: '', // 责任部门name
        responsiblePersonId: '', // 责任人id
        responsiblePersonName: '', // 责任人name
        materialTypeCode: [], // 耗材类型code
        materialTypeName: '' // 耗材分类名称
      },
      formLoading: false, // 表单loading
      pageLoading: false, // 初始化加载页面loading
      queryParams: {},
      unitsList: [],
      consumablesTypeList: [],
      allConsumablesTypeList: [], // 所有耗材
      rules: {
        takeStockName: [{ required: true, message: '请输入盘点单名称', trigger: 'blur' }],
        responsibleDepartmentId: [{ required: true, message: '请选择责任部门', trigger: 'change' }],
        responsiblePersonId: [{ required: true, message: '请选择责任人', trigger: 'change' }],
        time: [{ required: true, message: '请输入计划期限', trigger: 'change' }]
      },
      warehouseList: [],
      // 部门级联选择器配置
      manageUnitProps: {
        value: 'id',
        label: 'deptName',
        children: 'children',
        multiple: false,
        checkStrictly: true
      },
      materialTypeProps: {
        label: 'dictionaryDetailsName',
        children: 'children',
        value: 'id',
        multiple: true,
        checkStrictly: true,
        emitPath: false
      },
      personList: [],
      multipleSelection: [],
      tableData: [],
      responsibleUnitOptions: [], // 责任部门
      consumablesVisible: false
    }
  },
  computed: {
    pageTitle() {
      let title = '盘点单'
      switch (this.queryParams.type) {
        case 'add':
          title = '新建' + title
          break
        case 'edit':
          title = '编辑' + title
          break
        case 'detail':
          title += '详情'
          break
      }
      return title
    }
  },
  activated() {
    this.initEvent()
  },
  mounted() {
    this.initEvent()
  },
  methods: {
    initEvent() {
      // 重置form表单
      const data = this.$options.data()
      delete data.rules
      this.$nextTick(() => {
        Object.assign(this.$data, data) // 重置所有数据
        this.$refs.form.resetFields() // 重置表单校验状态
        this.getDeptList()
        this.getWarehouseData()
        this.getConsumableTypeList() // 耗材类型
        this.queryParams = this.$route.query
        if (this.queryParams.type !== 'add') {
          this.getStockInfo()
        }
      })
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.responsibleUnitOptions = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 获取仓库数据
    getWarehouseData() {
      let param = {
        pageSize: 99999,
        CurrentPage: 1,
        status: '0'
      }
      this.$api.warehouseApi
        .getWarehouseByPage(param)
        .then((res) => {
          if (res.code == 200) {
            this.warehouseList = res.data.list
          }
        })
        .catch((msg) => this.$message.error(msg || '获取仓库失败'))
    },
    // 获取仓库名称
    warehouseChange(val) {
      if (val && val.length > 0) {
        // 根据选中的仓库ID获取对应的仓库名称并用逗号拼接
        const selectedNames = val
          .map((id) => {
            const warehouse = this.warehouseList.find((item) => item.id === id)
            return warehouse ? warehouse.warehouseName : ''
          })
          .filter((name) => name !== '')
        this.form.warehouseName = selectedNames.join(',')
      } else {
        this.form.warehouseName = ''
      }
      this.tableData = []
    },
    // 耗材类型名称
    handleConsumablesTypeChange(val) {
      if (Array.isArray(val) && val.length > 0) {
        // 获取每个选中节点的完整路径
        const fullPathNames = val
          .map((id) => {
            // 获取该节点的完整路径
            return this.getNodeFullPath(this.consumablesTypeList, id)
          })
          .filter((path) => path !== null) // 过滤掉未找到的节点
        // 用逗号拼接所有路径
        this.form.materialTypeName = fullPathNames.join(',')
      } else {
        this.form.materialTypeName = ''
      }
      this.tableData = []
    },
    // 获取节点的完整路径
    getNodeFullPath(options, targetId, currentPath = []) {
      for (const node of options) {
        // 创建当前路径副本
        const newPath = [...currentPath, node.dictionaryDetailsName]
        if (node.id === targetId) {
          // 找到目标节点，返回完整路径
          return newPath.join('/')
        }
        // 递归查找子节点
        if (node.children && node.children.length > 0) {
          const result = this.getNodeFullPath(node.children, targetId, newPath)
          if (result) {
            return result
          }
        }
      }
      return null
    },
    // 查询字典
    getSelectByList(str, getArr) {
      this.$api.warehouseApi.getDictByPage({ dictionaryCategoryId: str, dictionaryDetailsStatus: '1' }).then((res) => {
        if (res.code == '200') {
          this[getArr] = res.data
        }
      })
    },
    // 查询耗材类型
    getConsumableTypeList() {
      this.$api.warehouseApi.getDictByPage({ dictionaryCategoryId: 'consumables_type', dictionaryDetailsStatus: '1' }).then((res) => {
        if (res.code == '200') {
          this.allConsumablesTypeList = res.data
          this.consumablesTypeList = transData(res.data, 'id', 'parentId', 'children')
        }
      })
    },
    // 选择管理部门
    selectManageUnit(val) {
      if (val && val.length > 0) {
        // 获取选中的节点路径名称
        const pathLabels = val.map((id) => this.findNodeLabel(this.responsibleUnitOptions, id)).filter(Boolean)
        this.form.responsibleDepartmentName = pathLabels.join('/')
        // 获取人员列表
        this.getLersonnelList([val[val.length - 1]])
      } else {
        this.form.responsibleDepartmentName = ''
        this.personList = []
        this.form.responsiblePersonId = ''
        this.form.responsiblePersonName = ''
      }
    },
    findNodeLabel(options, value) {
      for (const node of options) {
        if (node.id === value) {
          return node.deptName
        }
        if (node.children && node.children.length > 0) {
          const result = this.findNodeLabel(node.children, value)
          if (result) return result
        }
      }
      return null
    },
    // 获取人员列表
    getLersonnelList(deptIds) {
      let params = {
        current: 1,
        size: 9999,
        sex: '',
        pmId: '',
        postId: '', // 岗位
        stationStatus: '',
        officeId: deptIds.join(',')
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.personList = res.data.records
        }
      })
    },
    // 人员获取name
    selectManagerPerson(id) {
      const selected = this.personList.find((item) => item.id === id)
      if (selected) {
        this.form.responsiblePersonName = selected.staffName
      }
    },
    // 处理多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 修改批量删除方法
    handleBatchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      const selectedIds = new Set(this.multipleSelection.map((item) => item.uniqueId))
      this.tableData = this.tableData.filter((item) => !selectedIds.has(item.uniqueId))
    },
    addConsumables() {
      this.consumablesVisible = true
    },
    // 耗材数据
    handleConsumablesSelect(selectedData) {
      this.consumablesVisible = false
      this.tableData = [...selectedData]
    },
    closeDialog() {
      this.consumablesVisible = false
    },
    // 获取盘点单详情
    getStockInfo() {
      this.pageLoading = true
      this.$api.warehouseApi.getTakeStockById({ id: this.queryParams.id }).then((res) => {
        try {
          if (res.code == 200) {
            const data = res.data
            this.getLersonnelList([data.responsibleDepartmentId])
            for (const key in this.form) {
              if (Object.prototype.hasOwnProperty.call(this.form, key)) {
                this.$set(this.form, key, data[key])
              }
              this.form.time = [data.planStartDate, data.planEndDate]
            }
            this.tableData = data.takeStockSlaveList
            // warehouseId  的赋值逻辑
            const warehouseIds = data.warehouseId?.split(',') || []
            this.form.warehouseId = warehouseIds.length ? warehouseIds : data.warehouseId
            // materialTypeCode  的赋值逻辑
            const materialTypeCodes = data.materialTypeCode?.split(',') || []
            this.form.materialTypeCode = materialTypeCodes.length ? materialTypeCodes : data.materialTypeCode
            if (this.queryParams.type == 'edit') {
              this.form.id = data.id
            }
            this.pageLoading = false
          }
        } catch (error) {
          this.pageLoading = false
        }
      })
    },
    // 关闭
    onClose() {
      this.$router.go(-1)
    },
    // 保存
    onPreservation() {
      this.formLoading = true
      const {
        time,
        materialTypeCode,
        materialTypeName,
        responsibleDepartmentId,
        responsibleDepartmentName,
        responsiblePersonId,
        responsiblePersonName,
        takeStockCode,
        takeStockName,
        warehouseId,
        warehouseName
      } = this.form
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 检查是否有耗材数据
          if (!this.tableData || this.tableData.length === 0) {
            this.$message({
              message: '请添加耗材明细',
              type: 'warning'
            })
            this.formLoading = false
            return
          }
          let params = {
            userName: this.$store.state.user.userInfo.user.staffName,
            userId: this.$store.state.user.userInfo.user.staffId,
            saveData: {
              materialTypeCode: Array.isArray(materialTypeCode) ? materialTypeCode.join(',') : materialTypeCode,
              materialTypeName: materialTypeName,
              responsibleDepartmentId: Array.isArray(responsibleDepartmentId) ? responsibleDepartmentId[responsibleDepartmentId.length - 1] : responsibleDepartmentId,
              responsibleDepartmentName,
              responsiblePersonId,
              responsiblePersonName,
              takeStockCode,
              takeStockName,
              planStartDate: time[0],
              planEndDate: time[1],
              warehouseId: Array.isArray(warehouseId) ? warehouseId.join(',') : warehouseId,
              warehouseName: warehouseName,
              takeStockSlaveList: this.tableData
            }
          }
          if (this.queryParams.id) {
            params.id = this.queryParams.id
            params.saveData.id = this.queryParams.id
          }
          this.$api.warehouseApi
            .saveTakeStockInfo(params)
            .then((res) => {
              this.formLoading = false
              if (res.code == '200') {
                this.$message({
                  message: '保存成功',
                  type: 'success'
                })
                this.$router.go(-1)
              } else {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
              }
            })
            .catch((msg) => this.$message.error(msg || '保存失败'))
        } else {
          this.formLoading = false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.stockInventory-content {
  background-color: #fff;
  height: 100%;
  position: relative;
  .stockInventory-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
  }
  .content_box {
    padding: 0 36px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: calc(100% - 100px);
    .el-form-item {
      .el-input,
      .el-date-editor,
      .el-cascader,
      .el-select {
        width: 80%;
      }
    }
  }
}
.green_line {
  display: inline-block;
  width: 5px;
  height: 16px;
  background: #3562db;
  margin-right: 6px;
  border-radius: 2px !important;
  vertical-align: middle;
}
.toptip {
  box-sizing: border-box;
  padding-left: 26px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  padding-left: 16px;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}
::v-deep .el-date-editor .el-range__icon,
.el-range__close-icon {
  line-height: 28px !important;
}
</style>
