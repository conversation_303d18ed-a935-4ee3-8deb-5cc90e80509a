<template>
  <PageContainer class="stockInquiry-list">
    <template #content>
      <div class="stockInquiry-list__left">
        <div class="toptip">
          <div><span class="green_line"></span> 仓库</div>
        </div>
        <el-tree
          ref="treeRef"
          node-key="id"
          :data="treeData"
          :current-node-key="currentKey"
          :expand-on-click-node="false"
          default-expand-all
          highlight-current
          :props="{ label: 'warehouseName' }"
          class="dictionary-list__tree"
          @node-click="onTreeNodeClick"
        ></el-tree>
      </div>
      <div class="stockInquiry-list__right">
        <div class="stockInquiry-list__right__header">
          <el-form ref="formRef" :model="searchForm" class="stockInquiry-list__search" inline @submit.native.prevent="onSearch">
            <el-form-item prop="keyWords">
              <el-input v-model="searchForm.keyWords" placeholder="出库单号/耗材编码/耗材名称/申请人" clearable style="width: 280px"></el-input>
            </el-form-item>
            <el-form-item prop="outwarehouseType">
              <el-select v-model="searchForm.outwarehouseType" placeholder="出库类型" clearable filterable style="width: 150px">
                <el-option v-for="item in outboundTypeList" :key="item.dictionaryDetailsId" :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsId"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="materialTypeCode">
              <el-cascader
                v-model="searchForm.materialTypeCode"
                :options="materialTypeOptions"
                :props="{ checkStrictly: true, value: 'id', label: 'name', children: 'children' }"
                placeholder="耗材类型"
                clearable
                filterable
                style="width: 200px"
              ></el-cascader>
            </el-form-item>
            <el-form-item prop="applicantDepartmentId">
              <el-select v-model="searchForm.applicantDepartmentId" placeholder="申请部门" clearable filterable style="width: 150px">
                <el-option v-for="item in deptOptions" :key="item.id" :label="item.deptName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="time">
              <el-date-picker
                v-model="searchForm.time"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 240px"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="stockInquiry-list__right__actions">
            <el-button type="primary" plain @click="onReset">重置</el-button>
            <el-button type="primary" @click="onSearch">搜索</el-button>
          </div>
        </div>
        <div class="stockInquiry-list__right__operations">
          <el-button type="primary" plain @click="handlerExport">导出</el-button>
        </div>
        <div class="stockInquiry-list__statistics">
          <span>
            <span>出库总数</span>
            <span class="text-red">{{ totalNum }}</span>
          </span>
          <span class="price">
            <span>金额</span>
            <span class="text-red">{{ totalPrice }}</span>
          </span>
        </div>
        <div class="stockInquiry-list__table">
          <el-table v-loading="tableLoadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
            <el-table-column label="出库单号" prop="orderNumber" width="140" show-overflow-tooltip>
              <template #default="{ row }">
                <span style="color: rgb(0, 138, 244); cursor: pointer" @click="toDetail(row)">{{ row.orderNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column label="工单号" prop="workNum" show-overflow-tooltip> </el-table-column>
            <el-table-column label="出库仓库" prop="warehouseName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="出库类型" prop="outwarehouseTypeName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="申请人" prop="applicantName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="申请人部门" prop="applicantDepartmentName" width="100" show-overflow-tooltip> </el-table-column>
            <el-table-column label="出库日期" prop="createTime" show-overflow-tooltip> </el-table-column>
            <el-table-column label="耗材编码" prop="materialCode" show-overflow-tooltip> </el-table-column>
            <el-table-column label="耗材名称" prop="materialName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="耗材类型" prop="materialTypeName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="规格型号" prop="model" show-overflow-tooltip> </el-table-column>
            <el-table-column label="计量单位" prop="basicUnitName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="品牌" prop="brandName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="数量" prop="operateCount" show-overflow-tooltip> </el-table-column>
            <el-table-column label="单价" prop="unitPrice" show-overflow-tooltip> </el-table-column>
            <el-table-column label="金额" prop="sumAmount" show-overflow-tooltip> </el-table-column>
          </el-table>
          <el-pagination
            class="stockInquiry-list__pagination"
            :current-page="pagination.page"
            :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.size"
            :layout="pagination.layoutOptions"
            :total="pagination.total"
            @size-change="paginationSizeChange"
            @current-change="paginationCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </template>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
import tableListMixin from '@/mixins/tableListMixin'
import axios from 'axios'
export default {
  name: 'outboundDetails',
  mixins: [tableListMixin],
  data() {
    return {
      tableLoadingStatus: false,
      currentKey: '',
      searchForm: {
        keyWords: '', // 出库单号/耗材编码/耗材名称/申请人
        outwarehouseType: '', // 出库类型
        materialTypeCode: '', // 耗材类型
        applicantDepartmentId: '', // 申请部门
        time: []
      },
      treeData: [],
      tableData: [],
      treeLoadingStatus: false,
      materialTypeOptions: [],
      outboundTypeList: [],
      deptOptions: [],
      totalNum: 0,
      totalPrice: 0,
      exportLoading: false,
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  mounted() {
    this.getTreeData()
    this.getMaterialTypeOptions()
    this.getOutboundTypeList()
    this.getDeptOptions()
  },
  methods: {
    // 获取仓库树形数据
    getTreeData() {
      this.treeLoadingStatus = true
      let param = {
        status: '0'
      }
      this.$api.warehouseApi
        .getWarehouseByPage(param)
        .then((res) => {
          if (res.code == 200) {
            // 创建root节点，固定
            const root = { warehouseName: '全部', id: '', children: [] }
            root.children = res.data.list.map((it) => {
              // 设置父级ID
              it.parentId = root.id
              return it
            })
            this.treeData = [root]
            // 当树数据加载完成后，默认选中第一个节点
            const first = root
            this.currentKey = first.id
            this.onSearch()
            this.$nextTick(() => {
              this.$refs.treeRef.setCurrentKey(first.id)
            })
          }
          this.treeLoadingStatus = false
        })
        .catch((msg) => this.$message.error(msg || '获取仓库失败'))
        .finally(() => {
          this.treeLoadingStatus = false
        })
    },
    // 获取耗材类型选项
    getMaterialTypeOptions() {
      this.$api.warehouseApi
        .getDictByPage({ dictionaryCategoryId: 'consumables_type', dictionaryDetailsStatus: '1' })
        .then((res) => {
          if (res.code == '200') {
            this.materialTypeOptions = transData(res.data, 'id', 'parentId', 'children')
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取耗材类型失败'))
    },
    // 获取出库类型列表
    getOutboundTypeList() {
      const params = {
        pageSize: 99999,
        currentPage: 1,
        userType: 1,
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName,
        dictionaryCategoryId: 'outbound_type',
        dictionaryDetailsStatus: '1'
      }
      this.$api.warehouseApi
        .getDictByPage(params)
        .then((res) => {
          if (res.code == '200') {
            this.outboundTypeList = res.data
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取出库类型失败'))
    },
    // 获取部门选项
    getDeptOptions() {
      this.$api
        .getSelectedDept({})
        .then((res) => {
          if (res.code == 200) {
            this.deptOptions = res.data
          }
        })
        .catch((msg) => this.$message.error(msg || '获取部门数据失败'))
    },
    // 当树点击被点击时
    onTreeNodeClick(data) {
      if (data.id !== this.currentKey) {
        this.currentKey = data.id
        this.onSearch()
      }
    },
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoadingStatus = true
      const { keyWords, outwarehouseType, materialTypeCode, applicantDepartmentId, time } = this.searchForm
      const params = {
        pageSize: this.pagination.size,
        CurrentPage: this.pagination.current,
        warehouseId: this.currentKey,
        keyWords,
        outwarehouseType,
        applicantDepartmentId,
        beginDate: time && time.length ? time[0] : '',
        endDate: time && time.length ? time[1] : '',
        materialTypeCode: Array.isArray(materialTypeCode) ? materialTypeCode[materialTypeCode.length - 1] : '',
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      this.$api.warehouseApi
        .getOutwarehouseDetailByPage(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.sum
            this.totalNum = res.data.operateCount || 0
            this.totalPrice = res.data.sumAmount || 0
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.currentKey = ''
      this.$refs.formRef.resetFields()
      this.$nextTick(() => {
        this.$refs.treeRef.setCurrentKey(this.currentKey)
      })
      this.onSearch()
    },
    // 导出功能
    handlerExport() {
      this.exportLoading = true
      const userInfo = this.$store.state.user.userInfo.user
      const { keyWords, outwarehouseType, materialTypeCode, applicantDepartmentId, time } = this.searchForm
      const params = {
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        warehouseId: this.currentKey,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        keyWords,
        outwarehouseType,
        applicantDepartmentId,
        beginDate: time && time.length ? time[0] : '',
        endDate: time && time.length ? time[1] : '',
        materialTypeCode: Array.isArray(materialTypeCode) ? materialTypeCode[materialTypeCode.length - 1] : ''
      }
      const formData = new FormData()
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key])
      })
      axios({
        method: 'post',
        url: __PATH.BASE_URL_HSC + 'outwarehouseRecord/exportOutwarehouseDetail',
        data: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          this.$message.success(res.message || '导出成功')
          this.exportLoading = false
        })
        .catch(() => {
          this.$message.error('导出失败')
          this.exportLoading = false
        })
    },
    // 出库单详情
    toDetail(row) {
      this.$router.push({ name: 'outWarehouseManageDetails', query: { type: 'detail', recordNumber: row.orderNumber } })
    }
  }
}
</script>
<style scoped lang="scss">
.stockInquiry-list {
  ::v-deep(> .container-content) {
    display: flex;
    background-color: #fff;
  }
  &__left {
    width: 280px;
    border-right: solid 1px #eee;
    display: flex;
    flex-flow: column nowrap;
    &::v-deep(.el-tree) {
      height: calc(100% - 64px);
      overflow: auto;
      padding: 16px;
      .el-tree-node__content {
        line-height: 36px;
        height: 36px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    display: flex;
    flex-flow: column nowrap;
    .el-form-item {
      margin-bottom: 16px;
    }
    &__operations {
      margin-bottom: 16px;
    }
  }
  &__right__header {
    display: flex;
    justify-content: space-between;
  }
  &__right__actions {
    line-height: 40px;
  }
  &__table {
    height: calc(100% - 190px);
  }
  &__pagination {
    margin-top: 16px;
  }
  &__statistics {
    margin-bottom: 16px;
    .price {
      margin-left: 16px;
    }
    .text-red {
      color: red;
      font-weight: bold;
      margin-left: 8px;
    }
  }
}
.green_line {
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #5188fc;
  margin-right: 6px;
  border-radius: 2px !important;
  vertical-align: middle;
}
.toptip {
  box-sizing: border-box;
  padding-left: 26px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  padding-left: 16px;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}
</style>
