<template>
  <div class="main">
    <el-dialog width="80%" title="关联工单" :visible.sync="workOrderVisible" :before-close="closeDialog" append-to-body custom-class="model-dialog">
      <div class="outermost">
        <div v-loading="loading" class="content">
          <div class="topTools">
            <el-select v-model="searchForm.showTimeType" placeholder="申报时间" style="width: 150px; margin-right: 10px" @change="timeTypeChange">
              <el-option v-for="(item, index) in showTimeTypeOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-date-picker
              v-if="searchForm.showTimeType == '4'"
              v-model="searchForm.timeRange"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始时间"
              range-separator="至"
              end-placeholder="结束时间"
              style="margin-right: 10px"
              @change="timeRangeChange"
            >
            </el-date-picker>
            <el-select v-model="searchForm.flowcode" placeholder="工单状态" style="width: 150px; margin-right: 10px" clearable filterable>
              <el-option v-for="(item, index) in flowCodeOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-select v-model="searchForm.sourcesDept" style="width: 150px; margin-right: 10px" placeholder="申报科室" clearable filterable>
              <el-option v-for="(item, index) in sourcesDeptOptions" :key="index" :label="item.officeName" :value="item.id"> </el-option>
            </el-select>
            <el-select
              v-model="searchForm.designateDeptCode"
              style="width: 150px; margin-right: 10px"
              placeholder="服务班组"
              clearable
              filterable
              @change="designateDeptCodeChange"
            >
              <el-option v-for="(item, index) in designateDeptCodeOptions" :key="index" :label="item.teamName" :value="item.id"> </el-option>
            </el-select>
            <el-select v-model="searchForm.designatePersonCode" style="width: 150px; margin-right: 10px" placeholder="服务人员" clearable filterable>
              <el-option v-for="(item, index) in designatePersonCodeOptions" :key="index" :label="item.member_name" :value="item.id"> </el-option>
            </el-select>
            <el-input v-model="searchForm.workNum" placeholder="申报工单号" clearable style="width: 150px; margin-right: 10px"></el-input>
            <el-input v-model="searchForm.questionDescription" placeholder="申报描述" clearable style="width: 150px; margin-right: 10px"></el-input>
            <el-button type="primary" @click="reset">重 置</el-button>
            <el-button type="primary" @click="search">查 询</el-button>
          </div>

          <el-table
            ref="personTable"
            v-loading="tableLoading"
            :data="tableData"
            border
            :height="tableHeight"
            stripe
            class="single-select-table"
            :row-key="getRowKeys"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" :reserve-selection="true"></el-table-column>
            <el-table-column type="index" label="序号" width="75">
              <template slot-scope="scope">
                <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="工单号" prop="workNum" show-overflow-tooltip>
              <template slot-scope="scope">
                <span style="color: #5c81e3">{{ scope.row.workNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="开单时间" prop="createDate" show-overflow-tooltip></el-table-column>
            <el-table-column label="工单类型" prop="workTypeName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="工单状态" prop="flowtype" show-overflow-tooltip> </el-table-column>
            <el-table-column label="服务地点" prop="localtionNames" show-overflow-tooltip> </el-table-column>
            <el-table-column label="服务事项" prop="allItem" show-overflow-tooltip> </el-table-column>
            <el-table-column label="服务部门" prop="designateDeptName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="服务人员" prop="designatePersonName" show-overflow-tooltip> </el-table-column>
          </el-table>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-pagination
          class="user-pagination"
          style
          :current-page="currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :pager-count="5"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
        <el-button type="primary" @click="closeDialog">取 消</el-button>
        <el-button type="primary" class="sino-button-sure" @click="editSubmit">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    workOrderVisible: {
      type: Boolean,
      default: false
    },
    tableDate: {}
  },
  data() {
    return {
      searchForm: {
        workTypeCode: '',
        workTypeName: '',
        id: '',
        timeRange: [],
        showTimeType: '1',
        startTime: '',
        flowcode: '',
        endTime: '',
        orderBy: '',
        workNum: '',
        questionDescription: '',
        designateDeptCode: '',
        designatePersonCode: '',
        sourcesDeptName: '',
        sourcesDept: ''
      },
      timeRange: [],
      designatePersonCodeOptions: [], // 服务人员
      designateDeptCodeOptions: [], // 服务班组
      showTimeTypeOptions: [
        {
          label: '今天',
          value: '1'
        },
        {
          label: '昨天',
          value: '2'
        },
        {
          label: '本周',
          value: '3'
        },
        {
          label: '自定义',
          value: '4'
        }
      ],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      tableLoading: false,
      multipleSelection: [],
      tableData: [],
      loading: false,
      sourcesDeptOptions: [], // 申报科室
      flowCodeOptions: [
        // {
        //   label: '全部',
        //   value: ''
        // },
        {
          label: '未完工',
          value: '30'
        },
        {
          label: '未受理',
          value: '1'
        },
        {
          label: '暂存',
          value: '7'
        },
        {
          label: '未派工',
          value: '2'
        },
        {
          label: '已派工',
          value: '3'
        },
        {
          label: '已挂单',
          value: '4'
        },
        {
          label: '已完工',
          value: '5'
        },
        {
          label: '已取消',
          value: '6'
        }
      ]
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 525
    }
  },
  watch: {},
  mounted() {
    this.getOfficeList()
    this.getTeamList('')
    this.getOCTeamMemberList('')
    this.search()
  },
  methods: {
    // 获取申报科室
    getOfficeList() {
      this.$api.warehouseApi.getOfficeAll().then((res) => {
        if (res) {
          this.sourcesDeptOptions = res.body.data
        }
      })
    },
    // 获取班组
    getTeamList(val) {
      this.$api.oneStopApi.getOCTeamInfo({ id: val }).then((res) => {
        if (res) {
          this.designateDeptCodeOptions = res.data.list
        }
      })
    },
    // 时间类型改变
    timeTypeChange(val) {
      this.searchForm.timeRange = []
      this.searchForm.startTime = ''
      this.searchForm.endTime = ''
    },
    // 日期改变
    timeRangeChange(val) {
      if (val && val.length) {
        this.searchForm.startTime = val[0] // 开始时间
        this.searchForm.endTime = val[1] // 结束时间
      } else {
        this.searchForm.startTime = ''
        this.searchForm.endTime = ''
      }
    },
    // 服务班组改变
    designateDeptCodeChange(val) {
      this.searchForm.designatePersonCode = ''
      this.designatePersonCodeOptions = []
      if (val) {
        this.getOCTeamMemberList(val)
      }
    },

    // 获取服务人员
    getOCTeamMemberList(val) {
      this.$api.oneStopApi.getOCTeamMemberInfo({ id: val }).then((res) => {
        if (res) {
          this.designatePersonCodeOptions = res.data.list
        }
      })
    },
    getRowKeys(row) {
      return row.id
    },
    closeDialog() {
      this.$emit('closeWorkOrderDialog')
    },
    editSubmit() {
      if (this.multipleSelection.length > 0) {
        this.$emit('handleworkOrder', this.multipleSelection)
      } else {
        this.$message.error('请先选择工单')
      }
    },

    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.search()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.search()
    },
    handleSelectionChange(val) {
      // 单选处理逻辑
      if (val.length > 1) {
        this.$refs.personTable.clearSelection() // 清空所有选择
        this.$refs.personTable.toggleRowSelection(val.pop()) // 保留最后一个选中项
        return
      }
      this.multipleSelection = val
      console.log(this.multipleSelection)
    },
    search() {
      let params = {
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        ...this.searchForm
      }
      delete params.timeRange
      params.sourcesDeptName = params.sourcesDept

      this.tableLoading = true
      this.$api.warehouseApi.queryWorkOrderByPage(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    reset() {
      this.currentPage = 1
      this.searchForm = {
        workTypeCode: '',
        workTypeName: '',
        id: '',
        timeRange: [],
        showTimeType: '1',
        startTime: '',
        flowcode: '',
        endTime: '',
        orderBy: '',
        workNum: '',
        questionDescription: '',
        designateDeptCode: '',
        designatePersonCode: '',
        sourcesDeptName: '',
        sourcesDept: ''
      }
      this.timeRange = []
      this.search()
    }
  }
}
</script>

<style lang="scss" scoped>
.outermost {
  display: flex;
  width: 100%;
  height: 500px;
  border: 1px solid #eee;
  padding: 10px;
}

.content {
  background-color: #fff;
  padding: 10px;
  width: 100%;
  height: 100%;
}

.topTools {
  margin-bottom: 5px;
}

.user-pagination {
  text-align: right;
  position: absolute;
  right: 250px;
  bottom: 10px;
}

// @media screen and(max-width: 1600px) {
//   .user-pagination {
//     right: 210px;
//     bottom: 10px;
//   }
// }
.tree-title {
  border: 1px solid #ebeef5;
  width: 40%;
  margin-bottom: 5px;
  line-height: 30px;
  border-radius: 4px;
  text-align: center;
  color: #5188fc;
  cursor: pointer;
}

.tree-title-active {
  background: #5188fc;
  border-color: #5188fc;
  color: #fff;
}

.block {
  height: calc(100% - 80px);
  overflow: auto;
}

::v-deep .el-tree-node__label {
  display: inline-block;
  white-space: normal;
  text-align: left;
}

::v-deep .el-tree-node__content {
  height: auto;
}

::v-deep .single-select-table {
  .el-table__header-wrapper .el-checkbox {
    display: none;
  }
}
</style>
