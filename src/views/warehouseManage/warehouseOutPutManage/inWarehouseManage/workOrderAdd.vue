<template>
  <div class="main">
    <el-dialog width="80%" title="关联工单" :visible.sync="workOrderVisible" :before-close="closeDialog" append-to-body custom-class="model-dialog">
      <div class="outermost">
        <div v-loading="loading" class="content">
          <div class="topTools">
            <el-input v-model.trim="searchForm.workNum" placeholder="工单号" style="width: 150px; margin-right: 10px"> </el-input>
            <el-select v-model="searchForm.designatePersonCode" style="width: 150px; margin-right: 10px" placeholder="服务人员" clearable filterable>
              <el-option v-for="(item, index) in designatePersonCodeOptions" :key="index" :label="item.member_name" :value="item.id"> </el-option>
            </el-select>

            <el-select v-model="searchForm.keyWords" placeholder="工单类型" style="width: 150px; margin-right: 10px" clearable filterable>
              <el-option v-for="item in typeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
            <el-select v-model="searchForm.flowtype" placeholder="工单状态" style="width: 150px; margin-right: 10px" clearable filterable>
              <el-option v-for="item in flowCodeOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-input v-model="searchForm.localtionName" placeholder="请选择服务地点" clearable style="width: 150px; margin-right: 10px" @focus="getLocaltion"></el-input>
            <el-cascader
              ref="mattersCode"
              v-model="searchForm.itemServiceCode"
              style="width: 150px; margin-right: 10px"
              size="mini"
              filterable
              collapse-tags
              :props="mattersProp"
              :options="itemTreeData"
              placeholder="请选择服务事项"
              class="selectHeightClass"
              :show-all-levels="false"
            ></el-cascader>
            <el-select v-model="searchForm.sourcesDept" style="width: 150px; margin-right: 10px" placeholder="服务部门" clearable filterable>
              <el-option v-for="(item, index) in sourcesDeptOptions" :key="index" :label="item.officeName" :value="item.id"> </el-option>
            </el-select>
            <el-date-picker
              v-model="timeRange"
              style="margin-right: 10px"
              type="daterange"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始时间"
              range-separator="至"
              end-placeholder="结束时间"
              @change="timeRangeChange"
            >
            </el-date-picker>
            <el-button type="primary" @click="reset">重 置</el-button>
            <el-button type="primary" @click="search">查 询</el-button>
          </div>

          <el-table
            ref="personTable"
            v-loading="tableLoading"
            :data="tableData"
            border
            :height="tableHeight"
            stripe
            class="single-select-table"
            :row-key="getRowKeys"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" :reserve-selection="true"></el-table-column>
            <el-table-column type="index" label="序号" width="75">
              <template slot-scope="scope">
                <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="工单号" prop="workNum" show-overflow-tooltip>
              <template slot-scope="scope">
                <span style="color: #5c81e3">{{ scope.row.workNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="开单时间" prop="createDate" show-overflow-tooltip></el-table-column>
            <el-table-column label="工单类型" prop="workTypeName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="工单状态" prop="flowtype" show-overflow-tooltip> </el-table-column>
            <el-table-column label="服务地点" prop="localtionNames" show-overflow-tooltip> </el-table-column>
            <el-table-column label="服务事项" prop="allItem" show-overflow-tooltip> </el-table-column>
            <el-table-column label="服务部门" prop="designateDeptName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="服务人员" prop="designatePersonName" show-overflow-tooltip> </el-table-column>
          </el-table>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-pagination
          class="user-pagination"
          style
          :current-page="currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :pager-count="5"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
        <el-button type="primary" @click="closeDialog">取 消</el-button>
        <el-button type="primary" :disabled="addDis" class="sino-button-sure" @click="editSubmit">确认</el-button>
      </span>
    </el-dialog>
    <template v-if="changeLocationShow">
      <location ref="changeLocation" :changeLocationShow="changeLocationShow" @closeDialog="closeLocationDialog" @localSure="locationSure"></location>
    </template>
  </div>
</template>
<script>
import { transData } from '@/util'
import location from './location.vue'

export default {
  components: {
    location
  },
  props: {
    workOrderVisible: {
      type: Boolean,
      default: false
    },
    tableDate: {}
  },
  data() {
    return {
      mattersProp: {
        children: 'children',
        label: 'name',
        value: 'id',
        checkStrictly: true,
        multiple: true
      },
      itemTreeData: [],
      typeList: [],
      searchForm: {
        designatePersonCode: '',
        workNum: '',
        localtionName: '',
        itemServiceCode: [],
        sourcesDept: '',
        flowtype: '',
        startTime: '',
        endTime: ''
      },
      timeRange: [],
      designatePersonCodeOptions: [], // 服务人员
      searchDate: [],
      filterText: '',
      keyWords: '',
      total: 0,
      currentPage: 1,
      treeDataTop: '',
      data: [],
      pageSize: 10,
      tableLoading: false,
      treeLoading: false,
      multipleSelection: [],
      tableData: [],
      loading: false,
      addDis: false,
      changeLocationShow: false,
      sourcesDeptOptions: [], // 申报科室
      flowCodeOptions: [
        // {
        //   label: '全部',
        //   value: ''
        // },
        {
          label: '未完工',
          value: '30'
        },
        {
          label: '未受理',
          value: '1'
        },
        {
          label: '暂存',
          value: '7'
        },
        {
          label: '未派工',
          value: '2'
        },
        {
          label: '已派工',
          value: '3'
        },
        {
          label: '已挂单',
          value: '4'
        },
        {
          label: '已完工',
          value: '5'
        },
        {
          label: '已取消',
          value: '6'
        }
      ]
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 525
    }
  },
  watch: {},
  mounted() {
    this.getOfficeList()
    this.getItemTreeData()
    this.getOCTeamMemberList()
    this.treeDataTop = ''
    this.search()
  },
  methods: {
    // 获取申报科室
    getOfficeList() {
      this.$api.warehouseApi.getOfficeAll().then((res) => {
        if (res) {
          this.sourcesDeptOptions = res.body.data
        }
      })
    },
    // 日期改变
    timeRangeChange(val) {
      if (val && val.length) {
        this.searchForm.startTime = val[0] // 开始时间
        this.searchForm.endTime = val[1] // 结束时间
      } else {
        this.searchForm.startTime = ''
        this.searchForm.endTime = ''
      }
    },
    // 获取服务事项
    getItemTreeData() {
      this.$api.getItemTreeData().then((res) => {
        this.itemTreeData = res
      })
    },
    locationSure(data) {
      this.searchForm.localtionName = data.name
      this.changeLocationShow = false
    },
    closeLocationDialog() {
      this.changeLocationShow = false
    },

    getLocaltion() {
      this.changeLocationShow = true
    },
    // 获取服务人员
    getOCTeamMemberList(val) {
      this.$api.warehouseApi
        .getOCTeamMemberInfo({
          id: val
        })
        .then((res) => {
          if (res) {
            this.designatePersonCodeOptions = res.data.list
          }
        })
    },
    getRowKeys(row) {
      return row.id
    },
    closeDialog() {
      this.$emit('closeWorkOrderDialog')
    },
    editSubmit() {
      if (this.multipleSelection.length > 0) {
        this.$emit('handleworkOrder', this.multipleSelection)
      } else {
        this.$message.error('请先选择工单')
      }
    },

    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.search()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.search()
    },
    handleNodeClick(data) {
      this.currentPage = 1
      this.treeDataTop = data.id
      this.search()
    },
    handleSelectionChange(val) {
      // 单选处理逻辑
      if (val.length > 1) {
        this.$refs.personTable.clearSelection() // 清空所有选择
        this.$refs.personTable.toggleRowSelection(val.pop()) // 保留最后一个选中项
        return
      }
      this.multipleSelection = val
      console.log(this.multipleSelection)
    },
    search() {
      this.searchForm.itemServiceCode = this.searchForm.itemServiceCode.toString(',')
      const params = {
        pageSize: this.pageSize,
        CurrentPage: this.currentPage,
        ...this.searchForm
      }

      this.tableLoading = true
      this.$api.warehouseApi.queryWorkOrderByPage(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    reset() {
      this.currentPage = 1
      this.searchForm = {
        designatePersonCode: '',
        workNum: '',
        localtionName: '',
        itemServiceCode: [],
        sourcesDept: '',
        flowtype: '',
        startTime: '',
        endTime: ''
      }
      this.timeRange = []
      this.search()
    },
    filterNode(value, data) {
      if (!value) return true
      return data.dictionaryDetailsName.indexOf(value) !== -1
    }
  }
}
</script>

<style lang="scss" scoped>
.outermost {
  display: flex;
  width: 100%;
  height: 500px;
  border: 1px solid #eee;
  padding: 10px;
}

.content {
  background-color: #fff;
  padding: 10px;
  width: 100%;
  height: 100%;
}

.topTools {
  margin-bottom: 5px;
}

.user-pagination {
  text-align: right;
  position: absolute;
  right: 250px;
  bottom: 10px;
}

// @media screen and(max-width: 1600px) {
//   .user-pagination {
//     right: 210px;
//     bottom: 10px;
//   }
// }
.tree-title {
  border: 1px solid #ebeef5;
  width: 40%;
  margin-bottom: 5px;
  line-height: 30px;
  border-radius: 4px;
  text-align: center;
  color: #5188fc;
  cursor: pointer;
}

.tree-title-active {
  background: #5188fc;
  border-color: #5188fc;
  color: #fff;
}

.block {
  height: calc(100% - 80px);
  overflow: auto;
}

::v-deep .el-tree-node__label {
  display: inline-block;
  white-space: normal;
  text-align: left;
}

::v-deep .el-tree-node__content {
  height: auto;
}

::v-deep .single-select-table {
  .el-table__header-wrapper .el-checkbox {
    display: none;
  }
}
</style>
