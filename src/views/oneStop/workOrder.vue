<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-header">
        <el-tabs v-model="searchForm.workTypeCode" @tab-click="handleOrderTypeClick">
          <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="`${item.workTypeName}`" :name="item.workTypeCode" />
        </el-tabs>
        <div class="search-from">
          <div class="left">
            <el-select v-model="searchForm.showTimeType" placeholder="申报时间" @change="timeTypeChange">
              <el-option v-for="(item, index) in showTimeTypeOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-date-picker
              v-if="searchForm.showTimeType == '4'"
              v-model="searchForm.timeRange"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始时间"
              range-separator="至"
              end-placeholder="结束时间"
              @change="timeRangeChange"
            >
            </el-date-picker>
            <el-select v-model="searchForm.flowcode" placeholder="工单状态" clearable filterable>
              <el-option v-for="(item, index) in flowCodeOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-select v-model="searchForm.sourcesDept" placeholder="申报科室" clearable filterable>
              <el-option v-for="(item, index) in sourcesDeptOptions" :key="index" :label="item.officeName" :value="item.id"> </el-option>
            </el-select>
            <el-select v-model="searchForm.designateDeptCode" placeholder="服务班组" clearable filterable @change="designateDeptCodeChange">
              <el-option v-for="(item, index) in designateDeptCodeOptions" :key="index" :label="item.teamName" :value="item.id"> </el-option>
            </el-select>
            <el-select v-model="searchForm.designatePersonCode" placeholder="服务人员" clearable filterable>
              <el-option v-for="(item, index) in designatePersonCodeOptions" :key="index" :label="item.member_name" :value="item.id"> </el-option>
            </el-select>
            <el-input v-model="searchForm.workNum" placeholder="申报工单号" clearable></el-input>
            <el-input v-model="searchForm.questionDescription" placeholder="申报描述" clearable></el-input>
          </div>
          <div class="right">
            <el-button type="primary" plain @click="reset">重置</el-button>
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" icon="el-icon-search" @click="openSeach">高级搜索</el-button>
          </div>
        </div>
        <div class="batch-control">
          <div><el-button type="primary" @click="control('print')">打印</el-button> <el-button type="primary" @click="control('export')">导出</el-button></div>
          <img src="@/assets/images/setting.png" class="setting-icon" alt="设置" @click="handleSettingClick" />
        </div>
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="orderTable"
        :key="tableKey"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        row-key="id"
        :row-style="{ cursor: 'pointer' }"
        height="calc(100% - 40px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @row-click="handleRowClick"
        @pagination="paginationChange"
      >
        <!-- 自定义创建时间列的显示 -->
        <template #createDate="{ row }">
          {{ formatTime(row.createDate) }}
        </template>
        <!-- 自定义时间戳字段的显示 -->
        <template #disPlanSolutionTime="{ row }">
          {{ formatTimestamp(row.disPlanSolutionTime) }}
        </template>
        <template #disEntryOrdersDate="{ row }">
          {{ formatTimestamp(row.disEntryOrdersDate) }}
        </template>
        <!-- 自定义流程状态列的显示 -->
        <template #flowtype="{ row }">
          <span :style="row.flowtype === '未派工' ? 'color: red;' : ''">{{ row.flowtype }}</span>
        </template>
        <!-- 自定义响应时间列的显示 -->
        <template #responseTime="{ row }">
          <span :style="isResponseTimeOverdue(row) ? 'color: red;' : ''">
            {{ formatResponseTime(row) }}
          </span>
        </template>
        <!-- 自定义响应时间字符串列的显示 -->
        <template #responseTimeStr="{ row }">
          <span :style="isResponseTimeOverdue(row) ? 'color: red;' : ''">
            {{ formatResponseTime(row) }}
          </span>
        </template>
      </TablePage>
      <!-- 高级搜索 -->
      <advancedSearch :closeState="advancClose" class="advanced-search" @isCloseState="closeAdvancClose" @resetSearch="advancCloseReset" @searchList="advancSearchList">
        <template slot="content">
          <el-form ref="formInline" :model="advancedForm" :inline="true" class="advanced-search-form" label-position="right" label-width="auto">
            <el-form-item label="紧急程度" prop="urgencyDegree">
              <el-select v-model="advancedForm.urgencyDegree" placeholder="请选择紧急程度" clearable filterable>
                <el-option v-for="(item, index) in urgencyDegreeOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="申报来源" prop="workSources">
              <el-select v-model="advancedForm.workSources" placeholder="请选择申报来源" clearable filterable>
                <el-option v-for="(item, index) in workSourcesOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="申报属性" prop="typeSources">
              <el-select v-model="advancedForm.typeSources" placeholder="请选择申报属性" clearable filterable>
                <el-option v-for="(item, index) in typeSourcesOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="申报科室" prop="sourcesDept">
              <el-select v-model="advancedForm.sourcesDept" placeholder="请选择申报科室" clearable filterable>
                <el-option v-for="(item, index) in sourcesDeptOptions" :key="index" :label="item.officeName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item label="服务地点" prop="localtionName">
              <el-input v-model="advancedForm.localtionName" placeholder="请选择服务地点" clearable style="width: 200px" @focus="getLocaltion"></el-input>
            </el-form-item>
            <!-- <el-form-item label="服务商:" prop="companyCode">
              <el-select v-model="advancedForm.companyCode" placeholder="请选择服务商" clearable filterable @change="companyCodeChange">
                <el-option v-for="(item, index) in companyCodeOptions" :key="index" :label="item.company_name" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="服务班组:" prop="designateDeptCode">
              <el-select v-model="advancedForm.designateDeptCode" placeholder="请选择服务班组" clearable filterable @change="designateDeptCodeChange">
                <el-option v-for="(item, index) in designateDeptCodeOptions" :key="index" :label="item.teamName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="服务人员:" prop="designatePersonCode">
              <el-select v-model="advancedForm.designatePersonCode" placeholder="请选择服务人员" clearable filterable>
                <el-option v-for="(item, index) in designatePersonCodeOptions" :key="index" :label="item.member_name" :value="item.id"> </el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item label="响应时间" prop="responseTimeType">
              <el-select v-model="advancedForm.responseTimeType" placeholder="请选择响应时间" clearable filterable>
                <el-option v-for="(item, index) in responseTimeTypeOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="满意度评价" prop="disDegreeNew">
              <el-select v-model="advancedForm.disDegreeNew" placeholder="请选择满意度评价" clearable filterable>
                <el-option v-for="(item, index) in disDegreeNewOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="开单人" prop="callerName">
              <el-select v-model="advancedForm.callerName" placeholder="请选择开单人" clearable filterable>
                <el-option v-for="(item, index) in callerNameOptions" :key="index" :label="item.label" :value="item.label"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="开单人电话" prop="sourcesPhone">
              <el-input v-model="advancedForm.sourcesPhone" placeholder="请输入开单人电话" clearable style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="服务事项" prop="sourcesPhone">
              <SelectTree
                v-model="advancedForm.itemServiceCode"
                :level="'3'"
                :placeholder="'请选择服务事项'"
                :data="itemTreeData"
                :props="{ label: 'name', children: 'children' }"
                @getName="getItemServiceName"
              ></SelectTree>
            </el-form-item>
            <!-- <el-form-item label="申报描述" prop="questionDescription">
              <el-input v-model="advancedForm.questionDescription" placeholder="请输入申报描述" clearable style="width: 200px"></el-input>
            </el-form-item> -->
            <el-form-item label="跟踪状态" prop="feedbackFlag">
              <el-select v-model="advancedForm.feedbackFlag" placeholder="请选择跟踪状态" clearable filterable>
                <el-option v-for="(item, index) in feedbackFlagOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </template>
      </advancedSearch>
      <!-- 选择服务地点弹框 -->
      <template v-if="changeLocationShow">
        <Location ref="changeLocation" :changeLocationShow="changeLocationShow" @localSure="locationSure" @closeDialog="closeLocationDialog"></Location>
      </template>
      <!-- 配置表格 -->
      <configTable v-if="isConfigTableShow" :visible.sync="isConfigTableShow" :moduleId="searchForm.workTypeCode" @submitDialog="submitConfigTableDialog" />
      <div class="print-container">
        <OrderPrintTemplate ref="orderPrintRef" :data="orderPrintTemplateData" :column="orderPrintTemplateColumn" />
      </div>
      <!-- 工单详情弹窗 -->
      <el-dialog
        v-if="workOrderDetailVisible"
        :visible.sync="workOrderDetailVisible"
        custom-class="detailDialog main"
        :close-on-click-modal="false"
        :before-close="closeWorkOrderDetail"
      >
        <template slot="title">
          <span class="dialog-title">{{ dialogTitle }}</span>
        </template>
        <workOrderDetailList :rowData="detailObj" @close="closeWorkOrderDetail" />
      </el-dialog>
      <!-- 创建工单弹窗 -->
      <createdWorkOrder
        v-if="workOrderDialogVisible"
        :workOrderDealShow.sync="workOrderDialogVisible"
        :dealType="getDealType()"
        :workTypeName="workTypeName"
        :workTypeCode="workTypeCode"
        :workTypeId="detailObj && detailObj.id ? detailObj.id : ''"
        :shouldInitData="true"
        @workOrderSure="handleWorkOrderSure"
      ></createdWorkOrder>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import {
  flowCodeOptions,
  feedbackFlagOptions,
  showTimeTypeOptions,
  urgencyDegreeOptions,
  workSourcesOptions,
  typeSourcesOptions,
  responseTimeTypeOptions,
  disDegreeNewOptions
} from './workOrder.js'
import { listToTree } from '@/util'
import advancedSearch from '@/components/advancedSearch'
import OrderPrintTemplate from './components/OrderPrintTemplate.vue'
import configTable from './components/configTable.vue'
import Location from '@/components/CreatedWorkOrder/common/Location.vue'
import workOrderDetailList from '@/views/serviceQuality/maintenance/components/workOrderDetailList.vue'
import print from '@/util/print'
export default {
  name: 'workOrder',
  components: { advancedSearch, Location, configTable, OrderPrintTemplate, workOrderDetailList },
  data() {
    return {
      flowCodeOptions,
      feedbackFlagOptions,
      showTimeTypeOptions,
      urgencyDegreeOptions,
      workSourcesOptions,
      typeSourcesOptions,
      responseTimeTypeOptions,
      disDegreeNewOptions,
      advancClose: true,
      tableLoading: false,
      changeLocationShow: false,
      workOrderDetailVisible: false, // 工单详情弹窗
      workOrderDialogVisible: false, // 工单处理弹窗
      workTypeName: '', // 工单类型名称
      workTypeCode: '', // 工单类型编码
      detailObj: {}, // 当前选中的工单详情数据
      tableData: [],
      orderPrintTemplateData: [],
      orderPrintTemplateColumn: [],
      tableColumn: [],
      tableKey: 0,
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      },
      searchForm: {
        workTypeCode: '',
        workTypeName: '',
        id: '',
        timeRange: [],
        showTimeType: '1',
        startTime: '',
        flowcode: '',
        endTime: '',
        orderBy: '',
        workNum: '',
        questionDescription: '',
        designateDeptCode: '',
        designatePersonCode: '',
        sourcesDeptName: '',
        sourcesDept: ''
      },
      // 高级搜索
      advancedForm: {
        contrastType: '',
        free1: '',
        free2: '',
        urgencyDegree: '',
        disDegree: '',
        disDegreeNew: '',
        workSources: '',
        feedbackFlag: '',
        responseTime: '',
        responseTimeType: '',
        transportTypeCode: '',
        typeSources: '',
        sectionStartDate: '',
        sectionEndDate: '',
        sectionStartTime: '',
        sectionEndTime: '',
        region: '',
        buliding: '',
        storey: '',
        room: '',
        localtionName: '',
        replyToCode: '',
        companyCode: '',
        replyToPeople: '',
        callerName: '',
        sourcesPhone: '',
        itemDetailCode: '',
        itemDetailName: '',
        itemTypeCode: '',
        itemTypeName: '',
        itemServiceCode: '',
        itemServiceName: '',
        itemList: '',
        restaurantId: '',
        haveStartTime: '',
        haveEndTime: ''
      },
      sourcesDeptOptions: [], // 申报科室
      callerNameOptions: [], // 联系人
      companyCodeOptions: [], // 服务商
      designateDeptCodeOptions: [], // 班组
      designatePersonCodeOptions: [], // 服务人员
      itemTreeData: [],
      isConfigTableShow: false,
      tabList: []
    }
  },
  watch: {
    dialogVisible(newVal) {
      if (!newVal) {
        // 清空表格选中状态
        this.$nextTick(() => {
          if (this.$refs['el-table2']) {
            this.$refs['el-table2'].clearSelection()
          }
        })
      }
    }
  },
  mounted() {
    this.initEvent()
  },
  methods: {
    initEvent() {
      this.getTabList()
      this.getOfficeList()
      // this.getOutsourcingCompanyList()
      this.getTeamList('')
      this.getCallerRetrieveList()
      this.getItemTreeData()
      this.getOCTeamMemberList('') // 初始化加载全部服务人员
    },
    handleRowClick(row) {
      // 点击工单列表行时，如果是暂存状态(flowcode是7)，按照待办事项的处理方法走
      if (row.flowcode === '7') {
        // 暂存状态，按照待办事项处理方法
        console.log('处理暂存状态工单', row)
        // 设置工单类型信息
        this.workTypeCode = row.workTypeCode
        this.workTypeName = row.workTypeName
        this.detailObj = row
        // 打开工单处理弹窗
        this.workOrderDialogVisible = true
      } else {
        // 其他状态，打开工单详情弹窗
        this.detailObj = row
        if (row.flowtype) {
          this.dialogTitle = `${row.workTypeName}（${row.flowtype}）`
        } else {
          this.dialogTitle = `${row.workTypeName}`
        }
        this.workOrderDetailVisible = true
      }
    },
    closeWorkOrderDetail() {
      this.workOrderDetailVisible = false
      // 关闭弹框后刷新工单列表数据
      this.getDataList()
    },
    // 获取处理类型
    getDealType() {
      // 如果是暂存状态的工单，使用特殊的处理模式
      if (this.detailObj && this.detailObj.flowcode === '7') {
        return 'dealToAdd' // 暂存工单：回显用deal，提交用add
      }
      // 原有逻辑
      if (this.detailObj && this.detailObj.appointmentType === '1') {
        return 'update'
      }
      if (this.detailObj && this.detailObj.id) {
        return 'deal'
      }
      return 'add'
    },
    // 工单处理成功回调
    handleWorkOrderSure(data) {
      console.log('工单创建/处理成功', data)
      this.workOrderDialogVisible = false
      // 清空工单详情数据
      this.detailObj = {}
      // 刷新工单列表数据
      this.getDataList()
    },
    // 获取表格配置内容
    getCustomTableData() {
      this.tableLoading = true
      // 容错处理：优先取 user.userId，如果取不到则取 userId
      const userId = this.$store.state.user.userInfo.user?.userId || this.$store.state.user.userInfo.userId
      this.$api
        .getListColumnConfig({
          userId: userId,
          moduleId: this.searchForm.workTypeCode
        })
        .then((res) => {
          this.tableLoading = false
          let listColumnConfig = JSON.parse(res.data.configParams)
          let newColumns = listColumnConfig
            .filter((item) => item.isChecked === '1')
            .map((item) => {
              if (!item.id) {
                item.id = Math.floor(10000 + Math.random() * 90000)
                item.isInit = true
              }
              // 为需要特殊处理的列添加 slot
              const needsSlot = ['createDate', 'flowtype', 'responseTime', 'responseTimeStr', 'disPlanSolutionTime', 'disEntryOrdersDate'].includes(item.column)
              return {
                id: item.id,
                isInit: item.isInit,
                minWidth: item.width,
                prop: item.column,
                label: item.fieldName,
                slot: needsSlot ? item.column : '',
                align: 'center'
              }
            })
          this.tableColumn = [...newColumns]
          this.tableKey = Math.random() // 强制刷新组件
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 获取tab数据
    getTabList() {
      this.$api.oneStopApi.getWorkTypeList().then((res) => {
        if (res) {
          this.tabList = res.body ? res.body.data : []
          this.searchForm.workTypeCode = this.tabList[0].workTypeCode || '10'
          this.getCustomTableData()
          this.getDataList()
        } else {
          this.tabList = []
        }
      })
    },
    // 获取申报科室
    getOfficeList() {
      this.officeList = []
      this.$api.oneStopApi.getOfficeAll().then((res) => {
        if (res) {
          this.sourcesDeptOptions = res.body.data
        }
      })
    },
    // 获取联系人
    getCallerRetrieveList() {
      this.$api.oneStopApi.getCallerRetrieve().then((res) => {
        if (res) {
          this.callerNameOptions = res.body.result
        }
      })
    },
    // 获取服务商
    // getOutsourcingCompanyList() {
    //   this.$api.oneStopApi.getOutsourcingCompanyInfo().then((res) => {
    //     if (res.code == '200') {
    //       this.companyCodeOptions = res.data.list
    //     }
    //   })
    // },
    // companyCodeChange(val) {
    //   this.searchForm.designateDeptCode = ''
    //   this.searchForm.designatePersonCode = ''
    //   if (val) {
    //     this.getTeamList(val)
    //   } else {
    //     this.designateDeptCodeOptions = []
    //   }
    // },
    // 获取班组
    getTeamList(val) {
      this.$api.oneStopApi.getOCTeamInfo({ id: val }).then((res) => {
        if (res) {
          this.designateDeptCodeOptions = res.data.list
        }
      })
    },
    designateDeptCodeChange(val) {
      this.searchForm.designatePersonCode = ''
      if (val) {
        this.getOCTeamMemberList(val)
      } else {
        this.designatePersonCodeOptions = []
      }
    },
    // 获取服务人员
    getOCTeamMemberList(val) {
      this.$api.oneStopApi.getOCTeamMemberInfo({ id: val }).then((res) => {
        if (res) {
          this.designatePersonCodeOptions = res.data.list
        }
      })
    },
    // 服务事项返回数据
    getItemServiceName(item) {
      this.advancedFormitemServiceCode.itemServiceCode = item.id
      this.advancedFormitemServiceCode.itemServiceName = item.name
      const itemCodeArr = this.itemServiceCode.split('_')
      const itemCode = itemCodeArr[itemCodeArr.length - 1]
      this.getTeamsByWorkTypeCode(itemCode, 'update')
    },
    // 获取服务事项
    getItemTreeData() {
      const validWorkTypeCodes = ['1', '2']
      const workTypeCode = validWorkTypeCodes.includes(this.searchForm.workTypeCode) ? this.searchForm.workTypeCode : ''
      const params = {
        workTypeCode,
        free1: ''
      }
      if (validWorkTypeCodes.includes(this.searchForm.workTypeCode)) {
        // 符合条件时请求数据
        this.$api.getItemTreeData(params).then((res) => {
          this.itemTreeData = res ? listToTree(res, 'id', 'parent') : []
        })
      } else {
        // 不符合条件时直接清空 itemTreeData
        this.itemTreeData = []
      }
    },
    getLocaltion() {
      this.changeLocationShow = true
    },
    locationSure(item) {
      const parts = item.id.split('_')
      this.advancedForm.region = parts[0] || ''
      this.advancedForm.buliding = parts[1] || ''
      this.advancedForm.storey = parts[2] || ''
      this.advancedForm.room = parts[3] || ''
      this.advancedForm.localtionName = item.name // 已经是下划线分隔的格式
      this.changeLocationShow = false
    },
    // end
    closeLocationDialog() {
      this.changeLocationShow = false
    },
    // tabs切换
    handleOrderTypeClick(tab) {
      this.pageData.pageNo = 1
      this.getDataList()
      this.getCustomTableData()
      this.getItemTreeData()
      // 添加延时确保 table 已更新
      this.$nextTick(() => {
        this.resetTableScroll()
      })
    },
    resetTableScroll() {
      const tableRef = this.$refs.orderTable
      if (tableRef && tableRef.$el) {
        const bodyWrapper = tableRef.$el.querySelector('.el-table__body-wrapper')
        if (bodyWrapper) {
          bodyWrapper.scrollLeft = 0
        }
      }
    },
    // 关闭立即搜索
    closeAdvancClose(data) {
      this.advancClose = data
      // Object.keys(this.advancedForm).forEach((key) => {
      //   this.advancedForm[key] = ''
      // })
    },
    openSeach() {
      this.advancClose = !this.advancClose
    },
    advancSearchList() {
      this.advancClose = true
      this.search()
    },
    // 重置高级搜索
    advancCloseReset() {
      Object.keys(this.advancedForm).forEach((key) => {
        this.advancedForm[key] = ''
      })
      this.search()
    },
    reset() {
      this.searchForm.timeRange = []
      this.searchForm.showTimeType = '1'
      this.searchForm.startTime = ''
      this.searchForm.endTime = ''
      this.searchForm.flowcode = '' // 工单状态
      this.searchForm.workNum = '' // 工单号
      this.searchForm.questionDescription = '' // 申报描述
      this.searchForm.designateDeptCode = '' // 服务班组
      this.searchForm.designatePersonCode = '' // 服务人员
      this.searchForm.sourcesDept = '' // 申报科室
      this.searchForm.sourcesDeptName = '' // 申报科室
      Object.keys(this.advancedForm).forEach((key) => {
        this.advancedForm[key] = ''
      })
      this.search()
    },
    search() {
      this.pageData.current = 1
      this.getDataList()
    },
    // 获取工单列表
    getDataList() {
      let params = {
        pageNo: this.pageData.current,
        pageSize: this.pageData.size,
        ...this.advancedForm,
        ...this.searchForm
      }
      delete params.timeRange
      params.sourcesDeptName = params.sourcesDept
      this.tableLoading = true
      this.$api.oneStopApi
        .queryWorkOrderByPage(params)
        .then((res) => {
          this.tableLoading = false
          if (res) {
            this.tableData = res.rows ? res.rows : []
            this.pageData.total = res.total ? res.total : 0
          } else {
            this.tableData = []
            this.pageData.total = 0
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getDataList()
    },
    // 申报时间类型改变
    timeTypeChange(val) {
      if (val !== '4') {
        this.searchForm.timeRange = []
        this.searchForm.startTime = ''
        this.searchForm.endTime = ''
      }
    },
    // 日期改变
    timeRangeChange(val) {
      if (val && val.length) {
        this.searchForm.startTime = val[0] // 开始时间
        this.searchForm.endTime = val[1] // 结束时间
      } else {
        this.searchForm.startTime = ''
        this.searchForm.endTime = ''
      }
    },
    control(type, row) {
      if (type === 'print') {
        //  打印
        this.orderPrintTemplateData = this.tableData
        this.orderPrintTemplateColumn = this.tableColumn
        this.$nextTick(() => {
          print(this.$refs.orderPrintRef)
        })
      } else if (type === 'export') {
        let param = ''
        let params = {
          ...this.searchForm
        }
        param = __PATH.VUE_IOMS_API + 'sys/taskmanagementnew/olgTaskManagement/export?'
        for (let i in params) {
          param += i + '=' + (params[i] || '') + '&'
        }
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = param
        link.click()
      }
    },
    // 表格保存
    submitConfigTableDialog() {
      this.getCustomTableData()
      this.getDataList()
    },
    handleSettingClick() {
      this.isConfigTableShow = true
    },
    // 格式化时间显示（只显示时间部分）
    formatTime(dateStr) {
      if (!dateStr) return ''
      // 只保留时间部分 (时:分:秒)
      // return dateStr.split(' ')[1] || dateStr
      return dateStr
    },
    // 格式化时间戳为字符串
    formatTimestamp(timestamp) {
      if (!timestamp) return ''
      // 如果已经是字符串格式，直接返回
      if (typeof timestamp === 'string') return timestamp
      // 将时间戳转换为日期字符串
      const date = new Date(timestamp)
      if (isNaN(date.getTime())) return ''

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = date.getHours()
      const minutes = date.getMinutes()
      const seconds = date.getSeconds()

      // 如果时分秒都是0，只显示日期
      if (hours === 0 && minutes === 0 && seconds === 0) {
        return `${year}-${month}-${day}`
      }

      // 否则显示完整的日期时间
      const hoursStr = String(hours).padStart(2, '0')
      const minutesStr = String(minutes).padStart(2, '0')
      const secondsStr = String(seconds).padStart(2, '0')
      return `${year}-${month}-${day} ${hoursStr}:${minutesStr}:${secondsStr}`
    },
    // 格式化响应时间显示
    formatResponseTime(row) {
      // 优先使用 responseTimeStr，如果没有则使用 responseTime
      const responseTimeValue = row.responseTimeStr || row.responseTime
      // 未派工状态
      if (row.flowcode == '2' && !responseTimeValue) {
        if (this.isResponseTimeOverdue(row)) {
          return '已超时'
        } else {
          return '待派工'
        }
      } else {
        if (responseTimeValue == undefined || responseTimeValue == null || responseTimeValue === '') {
          return ''
        } else {
          // 如果是数字，添加"分钟"单位
          if (typeof responseTimeValue === 'number' || !isNaN(responseTimeValue)) {
            return responseTimeValue + '分钟'
          } else {
            // 如果是字符串，直接返回
            return responseTimeValue
          }
        }
      }
    },
    // 计算时间差（分钟）
    compareTime(date) {
      if (!date) return 0
      const now = new Date()
      const targetDate = new Date(date)
      return Math.floor((now - targetDate) / (1000 * 60))
    },
    // 判断响应时间是否超时
    isResponseTimeOverdue(row) {
      // 优先使用 responseTimeStr，如果没有则使用 responseTime
      const responseTimeValue = row.responseTimeStr || row.responseTime

      // 未派工状态
      if (row.flowcode == '2' && !responseTimeValue) {
        // 订餐服务
        if (row.workTypeCode == '4') {
          return this.compareTime(row.havemealsDate) >= row.overtime
        } else if (row.appointmentDate != undefined && row.appointmentDate != '') {
          return this.compareTime(row.appointmentDate) >= row.overtime
        } else {
          return this.compareTime(row.designateDeptDate) >= row.overtime
        }
      } else {
        // 如果是数字类型的响应时间，判断是否超过15分钟
        if (typeof responseTimeValue === 'number') {
          return responseTimeValue >= 15
        } else if (typeof responseTimeValue === 'string' && !isNaN(responseTimeValue)) {
          return parseInt(responseTimeValue) >= 15
        } else {
          // 如果是字符串类型且包含"超时"关键字，则认为超时
          return responseTimeValue && responseTimeValue.includes('超时')
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 0px 10px 10px 10px !important;
}
.search-header {
  padding: 0 10px;
  .search-from {
    padding-top: 16px;
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      flex-wrap: wrap;
      width: 75%;
      & > div {
        margin-right: 10px;
        padding-bottom: 10px;
      }
    }
    ::v-deep .el-date-editor .el-range__icon {
      line-height: 29px !important;
    }
    .el-input,
    .el-select {
      width: 150px !important;
    }
    .el-date-editor--datetimerange {
      width: 240px !important;
    }
  }
  .batch-control {
    display: flex;
    justify-content: space-between;
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 16px;
}
.setting-icon {
  width: 24px;
  height: 24px;
  margin-left: 8px;
  cursor: pointer;
}
.print-container {
  position: fixed;
  top: 100%;
}
::v-deep .el-range-editor.el-input__inner {
  padding: 1px 10px !important;
}
.red-text {
  color: red;
}
/* 工单详情弹框样式 */
::v-deep .detailDialog {
  .el-dialog__header {
    border-bottom: 1px solid #e6e6e6;
    padding: 14px 20px 12px;
    span {
      font-size: 16px;
    }
  }
  .el-dialog__body {
    height: 70vh;
    overflow-y: auto;
    padding: 15px;
  }
  .el-dialog__headerbtn {
    top: 13px;
    font-size: 18px;
  }
}
</style>
